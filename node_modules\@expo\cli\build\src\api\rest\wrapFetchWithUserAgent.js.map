{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithUserAgent.ts"], "sourcesContent": ["import process from 'node:process';\n\nimport { FetchLike } from './client.types';\nimport { Headers } from '../../utils/fetch';\n\nexport function wrapFetchWithUserAgent(fetch: FetchLike): FetchLike {\n  return (url, init = {}) => {\n    const headers = new Headers(init.headers);\n    // Version is added in the build script\n    headers.append('User-Agent', `expo-cli/${process.env.__EXPO_VERSION}`);\n    init.headers = headers;\n    return fetch(url, init);\n  };\n}\n"], "names": ["wrapFetchWithUserAgent", "fetch", "url", "init", "headers", "Headers", "append", "process", "env", "__EXPO_VERSION"], "mappings": "AAAA;;;;+BAKgBA,wBAAsB;;aAAtBA,sBAAsB;;;8DALlB,cAAc;;;;;;uBAGV,mBAAmB;;;;;;AAEpC,SAASA,sBAAsB,CAACC,KAAgB,EAAa;IAClE,OAAO,CAACC,GAAG,EAAEC,IAAI,GAAG,EAAE,GAAK;QACzB,MAAMC,OAAO,GAAG,IAAIC,MAAO,QAAA,CAACF,IAAI,CAACC,OAAO,CAAC,AAAC;QAC1C,uCAAuC;QACvCA,OAAO,CAACE,MAAM,CAAC,YAAY,EAAE,CAAC,SAAS,EAAEC,YAAO,EAAA,QAAA,CAACC,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvEN,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAC;QACvB,OAAOH,KAAK,CAACC,GAAG,EAAEC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC"}