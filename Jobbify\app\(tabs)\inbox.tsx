import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  RefreshControl,
  Alert,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { userDataService } from '@/services/userDataService';
import { useAuthCheck } from '@/utils/authUtils';

interface Message {
  id: string;
  contact: string;
  company: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: boolean;
}

interface Application {
  id: string;
  jobTitle: string;
  company: string;
  logo: string;
  appliedAt: string;
  status: string;
  statusColor: string;
}

const InboxScreen = () => {
  useAuthCheck();
  
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  const insets = useSafeAreaInsets();
  
  const [activeTab, setActiveTab] = useState<'applications' | 'messages'>('applications');
  const [applications, setApplications] = useState<Application[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  
  const slideAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadData();
  }, [user]);

  useEffect(() => {
    // Animate tab indicator
    Animated.spring(slideAnim, {
      toValue: activeTab === 'applications' ? 0 : 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [activeTab]);

  const loadData = async () => {
    if (!user) return;
    
    setLoadingApplications(true);
    setLoadingMessages(true);
    
    try {
      // Load applications
      const userApplications = await userDataService.getUserApplications(user.id);
      const formattedApplications: Application[] = userApplications.map(app => ({
        id: app.id,
        jobTitle: app.job_title,
        company: app.company,
        logo: app.logo_url || 'https://via.placeholder.com/50',
        appliedAt: new Date(app.applied_at).toLocaleDateString(),
        status: app.status || 'Applied',
        statusColor: getStatusColor(app.status || 'Applied'),
      }));
      setApplications(formattedApplications);
      
      // Load messages
      const userMessages = await userDataService.getUserMessages(user.id);
      const formattedMessages: Message[] = userMessages.map(msg => ({
        id: msg.id,
        contact: msg.contact_name,
        company: msg.company,
        avatar: msg.avatar_url || 'https://i.pravatar.cc/150?img=1',
        lastMessage: msg.last_message,
        timestamp: new Date(msg.timestamp).toLocaleDateString(),
        unread: msg.unread || false,
      }));
      setMessages(formattedMessages);
    } catch (error) {
      console.error('Error loading inbox data:', error);
      Alert.alert('Error', 'Failed to load inbox data');
    } finally {
      setLoadingApplications(false);
      setLoadingMessages(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'accepted':
      case 'hired':
        return '#10B981';
      case 'rejected':
        return '#EF4444';
      case 'interview':
        return '#F59E0B';
      case 'pending':
      case 'applied':
      default:
        return '#6B7280';
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const renderApplication = ({ item }: { item: Application }) => (
    <TouchableOpacity 
      style={[
        styles.modernItemContainer, 
        { 
          backgroundColor: themeColors.card,
          shadowColor: theme === 'dark' ? '#000' : '#000',
        }
      ]}
      activeOpacity={0.7}
    >
      <View style={styles.itemRow}>
        <View style={styles.logoContainer}>
          {item.logo && item.logo.trim() !== '' && !item.logo.includes('placeholder') ? (
            <Image source={{ uri: item.logo }} style={styles.modernLogo} />
          ) : (
            <View style={[styles.modernLogo, { backgroundColor: themeColors.cardSecondary, justifyContent: 'center', alignItems: 'center' }]}>
              <Text style={[styles.logoFallbackText, { color: themeColors.text }]} numberOfLines={2}>
                {item.jobTitle}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.itemContent}>
          <Text style={[styles.modernItemTitle, { color: themeColors.text }]} numberOfLines={1}>
            {item.jobTitle}
          </Text>
          <View style={styles.companyRow}>
            <Ionicons name="business-outline" size={14} color={themeColors.textSecondary} />
            <Text style={[styles.modernItemSubtitle, { color: themeColors.textSecondary }]}>
              {item.company}
            </Text>
          </View>
          <View style={styles.dateRow}>
            <Ionicons name="calendar-outline" size={14} color={themeColors.textSecondary} />
            <Text style={[styles.modernItemDate, { color: themeColors.textSecondary }]}>
              Applied {item.appliedAt}
            </Text>
          </View>
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.modernStatusBadge, { backgroundColor: item.statusColor + '20' }]}>
            <View style={[styles.statusDot, { backgroundColor: item.statusColor }]} />
            <Text style={[styles.modernStatusText, { color: item.statusColor }]}>
              {item.status}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMessage = ({ item }: { item: Message }) => (
    <TouchableOpacity 
      style={[
        styles.modernItemContainer, 
        { 
          backgroundColor: themeColors.card,
          shadowColor: theme === 'dark' ? '#000' : '#000',
          borderLeftWidth: item.unread ? 3 : 0,
          borderLeftColor: item.unread ? '#3B82F6' : 'transparent',
        }
      ]}
      activeOpacity={0.7}
    >
      <View style={styles.itemRow}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: item.avatar }} style={styles.modernAvatar} />
          {item.unread && <View style={styles.modernUnreadDot} />}
        </View>
        <View style={styles.itemContent}>
          <View style={styles.messageHeader}>
            <Text style={[styles.modernItemTitle, { color: themeColors.text }]} numberOfLines={1}>
              {item.contact}
            </Text>
            <Text style={[styles.modernTimestamp, { color: themeColors.textSecondary }]}>
              {item.timestamp}
            </Text>
          </View>
          <View style={styles.companyRow}>
            <Ionicons name="business-outline" size={14} color={themeColors.textSecondary} />
            <Text style={[styles.modernItemSubtitle, { color: themeColors.textSecondary }]}>
              {item.company}
            </Text>
          </View>
          <Text 
            style={[
              styles.modernMessageText, 
              { 
                color: themeColors.textSecondary,
                fontWeight: item.unread ? '500' : '400'
              }
            ]} 
            numberOfLines={2}
          >
            {item.lastMessage}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background, paddingTop: insets.top }]}>
      {/* Modern Header */}
      <LinearGradient
        colors={theme === 'dark' ? ['#1F2937', '#111827'] : ['#F8FAFC', '#F1F5F9']}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.modernHeaderTitle, { color: themeColors.text }]}>Inbox</Text>
            <Text style={[styles.headerSubtitle, { color: themeColors.textSecondary }]}>
              Stay connected with your opportunities
            </Text>
          </View>
          <TouchableOpacity style={[styles.notificationButton, { backgroundColor: themeColors.card }]}>
            <Ionicons name="notifications-outline" size={24} color={themeColors.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Modern Tab Selector */}
      <View style={[styles.modernTabSelector, { backgroundColor: themeColors.background }]}>
        <View style={[styles.modernTabContainer, { backgroundColor: themeColors.card }]}>
          <TouchableOpacity
            style={[
              styles.modernTabButton,
              activeTab === 'applications' && styles.activeTab,
              activeTab === 'applications' && { backgroundColor: '#3B82F6' }
            ]}
            onPress={() => setActiveTab('applications')}
            activeOpacity={0.8}
          >
            <Ionicons 
              name="document-text-outline" 
              size={18} 
              color={activeTab === 'applications' ? '#FFFFFF' : themeColors.textSecondary} 
            />
            <Text
              style={[
                styles.modernTabText,
                { color: themeColors.textSecondary },
                activeTab === 'applications' && { color: '#FFFFFF', fontWeight: '600' }
              ]}
            >
              Applications
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.modernTabButton,
              activeTab === 'messages' && styles.activeTab,
              activeTab === 'messages' && { backgroundColor: '#3B82F6' }
            ]}
            onPress={() => setActiveTab('messages')}
            activeOpacity={0.8}
          >
            <Ionicons 
              name="chatbubble-outline" 
              size={18} 
              color={activeTab === 'messages' ? '#FFFFFF' : themeColors.textSecondary} 
            />
            <Text
              style={[
                styles.modernTabText,
                { color: themeColors.textSecondary },
                activeTab === 'messages' && { color: '#FFFFFF', fontWeight: '600' }
              ]}
            >
              Messages
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'applications' ? (
          <FlatList
            data={applications}
            renderItem={renderApplication}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={themeColors.tint}
              />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: themeColors.textSecondary }]}>
                  {loadingApplications ? 'Loading applications...' : 'No applications yet'}
                </Text>
              </View>
            }
          />
        ) : (
          <FlatList
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={themeColors.tint}
              />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: themeColors.textSecondary }]}>
                  {loadingMessages ? 'Loading messages...' : 'No messages yet'}
                </Text>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Modern Header Styles
  modernHeader: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 32,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
    opacity: 0.8,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  // Modern Tab Styles
  modernTabSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modernTabContainer: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  modernTabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  activeTab: {
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  modernTabText: {
    fontSize: 15,
    fontWeight: '500',
  },
  // Content Styles
  content: {
    flex: 1,
  },
  listContent: {
    padding: 20,
    paddingTop: 8,
  },
  // Modern Item Container Styles
  modernItemContainer: {
    marginBottom: 16,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    overflow: 'hidden',
  },
  itemRow: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
  },
  // Logo and Avatar Styles
  logoContainer: {
    marginRight: 16,
  },
  modernLogo: {
    width: 56,
    height: 56,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  avatarContainer: {
    marginRight: 16,
    position: 'relative',
  },
  modernAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#F3F4F6',
  },
  modernUnreadDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#3B82F6',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  // Content Styles
  itemContent: {
    flex: 1,
  },
  modernItemTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 6,
    letterSpacing: -0.2,
  },
  companyRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  modernItemSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  modernItemDate: {
    fontSize: 13,
    opacity: 0.8,
  },
  // Message Specific Styles
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  modernTimestamp: {
    fontSize: 12,
    opacity: 0.7,
  },
  modernMessageText: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
  },
  // Status Styles
  statusContainer: {
    alignItems: 'flex-end',
  },
  modernStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  modernStatusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  // Empty State Styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.6,
  },
  logoFallbackText: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
    paddingHorizontal: 4,
  },
});

export default InboxScreen;