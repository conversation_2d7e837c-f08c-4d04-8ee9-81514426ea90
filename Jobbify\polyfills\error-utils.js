// Polyfill for ErrorUtils.setGlobalHandler to fix Hermes runtime issues
// This addresses the "Cannot read property 'setGlobalHandler' of undefined" error

// Initialize error handling as early as possible
const initializeErrorUtils = () => {
  const errorUtils = {
    setGlobalHandler: (handler) => {
      // Store the handler for potential use
      if (typeof global !== 'undefined') {
        global.__errorHandler = handler;
      }
      
      // Set up error handling for unhandled promise rejections
      if (typeof global !== 'undefined' && typeof global.addEventListener === 'function') {
        global.addEventListener('unhandledrejection', (event) => {
          if (handler && typeof handler === 'function') {
            try {
              handler(event.reason, true);
            } catch (e) {
              console.error('Error in global error handler:', e);
            }
          }
        });
      }
      
      // Set up error handling for uncaught exceptions
      if (typeof global !== 'undefined' && typeof global.addEventListener === 'function') {
        global.addEventListener('error', (event) => {
          if (handler && typeof handler === 'function') {
            try {
              handler(event.error, false);
            } catch (e) {
              console.error('Error in global error handler:', e);
            }
          }
        });
      }
      
      // For React Native, also handle process uncaught exceptions if available
      if (typeof process !== 'undefined' && process.on) {
        process.on('uncaughtException', (error) => {
          if (handler && typeof handler === 'function') {
            try {
              handler(error, false);
            } catch (e) {
              console.error('Error in global error handler:', e);
            }
          }
        });
        
        process.on('unhandledRejection', (reason) => {
          if (handler && typeof handler === 'function') {
            try {
              handler(reason, true);
            } catch (e) {
              console.error('Error in global error handler:', e);
            }
          }
        });
      }
    },
    
    getGlobalHandler: () => {
      return (typeof global !== 'undefined' && global.__errorHandler) || null;
    },
    
    reportError: (error) => {
      console.error('ErrorUtils.reportError:', error);
      const handler = (typeof global !== 'undefined' && global.__errorHandler) || null;
      if (handler && typeof handler === 'function') {
        try {
          handler(error, false);
        } catch (e) {
          console.error('Error in global error handler:', e);
        }
      }
    },
    
    reportFatalError: (error) => {
      console.error('ErrorUtils.reportFatalError:', error);
      const handler = (typeof global !== 'undefined' && global.__errorHandler) || null;
      if (handler && typeof handler === 'function') {
        try {
          handler(error, true);
        } catch (e) {
          console.error('Error in global error handler:', e);
        }
      }
    }
  };
  
  return errorUtils;
};

// Apply to all possible global contexts
const errorUtils = initializeErrorUtils();

if (typeof global !== 'undefined') {
  global.ErrorUtils = errorUtils;
}

if (typeof globalThis !== 'undefined') {
  globalThis.ErrorUtils = errorUtils;
}

if (typeof window !== 'undefined') {
  window.ErrorUtils = errorUtils;
}

// For React Native environments, also check if we need to polyfill on the global object
if (typeof window !== 'undefined' && !window.ErrorUtils) {
  window.ErrorUtils = global?.ErrorUtils || {
    setGlobalHandler: () => {},
    getGlobalHandler: () => null,
    reportError: (error) => console.error('Error:', error),
    reportFatalError: (error) => console.error('Fatal Error:', error)
  };
}

console.log('ErrorUtils polyfill loaded successfully');