{"version": 3, "sources": ["../../../src/serve/serveAsync.ts"], "sourcesContent": ["import { create<PERSON>e<PERSON><PERSON>and<PERSON> } from '@expo/server/build/vendor/http';\nimport chalk from 'chalk';\nimport connect from 'connect';\nimport http from 'http';\nimport path from 'path';\nimport send from 'send';\n\nimport * as Log from '../log';\nimport { directoryExistsAsync, fileExistsAsync } from '../utils/dir';\nimport { CommandError } from '../utils/errors';\nimport { findUpProjectRootOrAssert } from '../utils/findUp';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { resolvePortAsync } from '../utils/port';\n\ntype Options = {\n  port?: number;\n  isDefaultDirectory: boolean;\n};\n\nconst debug = require('debug')('expo:serve') as typeof console.log;\n\n// Start a basic http server\nexport async function serveAsync(inputDir: string, options: Options) {\n  const projectRoot = findUpProjectRootOrAssert(inputDir);\n\n  setNodeEnv('production');\n  require('@expo/env').load(projectRoot);\n\n  const port = await resolvePortAsync(projectRoot, {\n    defaultPort: options.port,\n    fallbackPort: 8081,\n  });\n\n  if (port == null) {\n    throw new CommandError('Could not start server. Port is not available.');\n  }\n  options.port = port;\n\n  const serverDist = options.isDefaultDirectory ? path.join(inputDir, 'dist') : inputDir;\n  //  TODO: `.expo/server/ios`, `.expo/server/android`, etc.\n\n  if (!(await directoryExistsAsync(serverDist))) {\n    throw new CommandError(\n      `The server directory ${serverDist} does not exist. Run \\`npx expo export\\` first.`\n    );\n  }\n\n  const isStatic = await isStaticExportAsync(serverDist);\n\n  Log.log(chalk.dim(`Starting ${isStatic ? 'static ' : ''}server in ${serverDist}`));\n\n  if (isStatic) {\n    await startStaticServerAsync(serverDist, options);\n  } else {\n    await startDynamicServerAsync(serverDist, options);\n  }\n  Log.log(`Server running at http://localhost:${options.port}`);\n  // Detect the type of server we need to setup:\n}\n\nasync function startStaticServerAsync(dist: string, options: Options) {\n  const server = http.createServer((req, res) => {\n    // Remove query strings and decode URI\n    const filePath = decodeURI(req.url?.split('?')[0] ?? '');\n\n    send(req, filePath, {\n      root: dist,\n      index: 'index.html',\n    })\n      .on('error', (err: any) => {\n        if (err.status === 404) {\n          res.statusCode = 404;\n          res.end('Not Found');\n          return;\n        }\n        res.statusCode = err.status || 500;\n        res.end('Internal Server Error');\n      })\n      .pipe(res);\n  });\n\n  server.listen(options.port!);\n}\n\nasync function startDynamicServerAsync(dist: string, options: Options) {\n  const middleware = connect();\n\n  const staticDirectory = path.join(dist, 'client');\n  const serverDirectory = path.join(dist, 'server');\n\n  const serverHandler = createRequestHandler({ build: serverDirectory });\n\n  // DOM component CORS support\n  middleware.use((req, res, next) => {\n    // TODO: Only when origin is `file://` (iOS), and Android equivalent.\n\n    // Required for DOM components security in release builds.\n\n    res.setHeader('Access-Control-Allow-Origin', '*');\n    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');\n    res.setHeader(\n      'Access-Control-Allow-Headers',\n      'Origin, X-Requested-With, Content-Type, Accept, expo-platform'\n    );\n\n    // Handle OPTIONS preflight requests\n    if (req.method === 'OPTIONS') {\n      res.statusCode = 200;\n      res.end();\n      return;\n    }\n    next();\n  });\n\n  middleware.use((req, res, next) => {\n    if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n      return next();\n    }\n\n    const pathname = canParseURL(req.url) ? new URL(req.url).pathname : req.url;\n    if (!pathname) {\n      return next();\n    }\n\n    debug(`Maybe serve static:`, pathname);\n\n    const stream = send(req, pathname, {\n      root: staticDirectory,\n      extensions: ['html'],\n    });\n\n    // add file listener for fallthrough\n    let forwardError = false;\n    stream.on('file', function onFile() {\n      // once file is determined, always forward error\n      forwardError = true;\n    });\n\n    // forward errors\n    stream.on('error', function error(err: any) {\n      if (forwardError || !(err.statusCode < 500)) {\n        next(err);\n        return;\n      }\n\n      next();\n    });\n\n    // pipe\n    stream.pipe(res);\n  });\n\n  middleware.use(serverHandler);\n\n  middleware.listen(options.port!);\n}\n\nfunction canParseURL(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nasync function isStaticExportAsync(dist: string): Promise<boolean> {\n  const routesFile = path.join(dist, `server/_expo/routes.json`);\n  return !(await fileExistsAsync(routesFile));\n}\n"], "names": ["serveAsync", "debug", "require", "inputDir", "options", "projectRoot", "findUpProjectRootOrAssert", "setNodeEnv", "load", "port", "resolvePortAsync", "defaultPort", "fallback<PERSON>ort", "CommandError", "serverDist", "isDefaultDirectory", "path", "join", "directoryExistsAsync", "isStatic", "isStaticExportAsync", "Log", "log", "chalk", "dim", "startStaticServerAsync", "startDynamicServerAsync", "dist", "server", "http", "createServer", "req", "res", "filePath", "decodeURI", "url", "split", "send", "root", "index", "on", "err", "status", "statusCode", "end", "pipe", "listen", "middleware", "connect", "staticDirectory", "serverDirectory", "serverHandler", "createRequestHandler", "build", "use", "next", "<PERSON><PERSON><PERSON><PERSON>", "method", "pathname", "canParseURL", "URL", "stream", "extensions", "forward<PERSON><PERSON>r", "onFile", "error", "routesFile", "fileExistsAsync"], "mappings": "AAAA;;;;+BAsBsBA,YAAU;;aAAVA,UAAU;;;yBAtBK,gCAAgC;;;;;;;8DACnD,OAAO;;;;;;;8DACL,SAAS;;;;;;;8DACZ,MAAM;;;;;;;8DACN,MAAM;;;;;;;8DACN,MAAM;;;;;;2DAEF,QAAQ;qBACyB,cAAc;wBACvC,iBAAiB;wBACJ,iBAAiB;yBAChC,kBAAkB;sBACZ,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,AAAsB,AAAC;AAG5D,eAAeF,UAAU,CAACG,QAAgB,EAAEC,OAAgB,EAAE;IACnE,MAAMC,WAAW,GAAGC,IAAAA,OAAyB,0BAAA,EAACH,QAAQ,CAAC,AAAC;IAExDI,IAAAA,QAAU,WAAA,EAAC,YAAY,CAAC,CAAC;IACzBL,OAAO,CAAC,WAAW,CAAC,CAACM,IAAI,CAACH,WAAW,CAAC,CAAC;IAEvC,MAAMI,IAAI,GAAG,MAAMC,IAAAA,KAAgB,iBAAA,EAACL,WAAW,EAAE;QAC/CM,WAAW,EAAEP,OAAO,CAACK,IAAI;QACzBG,YAAY,EAAE,IAAI;KACnB,CAAC,AAAC;IAEH,IAAIH,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,IAAII,OAAY,aAAA,CAAC,gDAAgD,CAAC,CAAC;IAC3E,CAAC;IACDT,OAAO,CAACK,IAAI,GAAGA,IAAI,CAAC;IAEpB,MAAMK,UAAU,GAAGV,OAAO,CAACW,kBAAkB,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACd,QAAQ,EAAE,MAAM,CAAC,GAAGA,QAAQ,AAAC;IACvF,0DAA0D;IAE1D,IAAI,CAAE,MAAMe,IAAAA,IAAoB,qBAAA,EAACJ,UAAU,CAAC,AAAC,EAAE;QAC7C,MAAM,IAAID,OAAY,aAAA,CACpB,CAAC,qBAAqB,EAAEC,UAAU,CAAC,+CAA+C,CAAC,CACpF,CAAC;IACJ,CAAC;IAED,MAAMK,QAAQ,GAAG,MAAMC,mBAAmB,CAACN,UAAU,CAAC,AAAC;IAEvDO,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,SAAS,EAAEL,QAAQ,GAAG,SAAS,GAAG,EAAE,CAAC,UAAU,EAAEL,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnF,IAAIK,QAAQ,EAAE;QACZ,MAAMM,sBAAsB,CAACX,UAAU,EAAEV,OAAO,CAAC,CAAC;IACpD,OAAO;QACL,MAAMsB,uBAAuB,CAACZ,UAAU,EAAEV,OAAO,CAAC,CAAC;IACrD,CAAC;IACDiB,IAAG,CAACC,GAAG,CAAC,CAAC,mCAAmC,EAAElB,OAAO,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9D,8CAA8C;AAChD,CAAC;AAED,eAAegB,sBAAsB,CAACE,IAAY,EAAEvB,OAAgB,EAAE;IACpE,MAAMwB,MAAM,GAAGC,MAAI,EAAA,QAAA,CAACC,YAAY,CAAC,CAACC,GAAG,EAAEC,GAAG,GAAK;YAElBD,GAAO;QADlC,sCAAsC;QACtC,MAAME,QAAQ,GAAGC,SAAS,CAACH,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACI,GAAG,SAAO,GAAdJ,KAAAA,CAAc,GAAdA,GAAO,CAAEK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,EAAE,CAAC,AAAC;QAEzDC,IAAAA,KAAI,EAAA,QAAA,EAACN,GAAG,EAAEE,QAAQ,EAAE;YAClBK,IAAI,EAAEX,IAAI;YACVY,KAAK,EAAE,YAAY;SACpB,CAAC,CACCC,EAAE,CAAC,OAAO,EAAE,CAACC,GAAQ,GAAK;YACzB,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;gBACtBV,GAAG,CAACW,UAAU,GAAG,GAAG,CAAC;gBACrBX,GAAG,CAACY,GAAG,CAAC,WAAW,CAAC,CAAC;gBACrB,OAAO;YACT,CAAC;YACDZ,GAAG,CAACW,UAAU,GAAGF,GAAG,CAACC,MAAM,IAAI,GAAG,CAAC;YACnCV,GAAG,CAACY,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACnC,CAAC,CAAC,CACDC,IAAI,CAACb,GAAG,CAAC,CAAC;IACf,CAAC,CAAC,AAAC;IAEHJ,MAAM,CAACkB,MAAM,CAAC1C,OAAO,CAACK,IAAI,CAAE,CAAC;AAC/B,CAAC;AAED,eAAeiB,uBAAuB,CAACC,IAAY,EAAEvB,OAAgB,EAAE;IACrE,MAAM2C,UAAU,GAAGC,IAAAA,QAAO,EAAA,QAAA,GAAE,AAAC;IAE7B,MAAMC,eAAe,GAAGjC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACU,IAAI,EAAE,QAAQ,CAAC,AAAC;IAClD,MAAMuB,eAAe,GAAGlC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACU,IAAI,EAAE,QAAQ,CAAC,AAAC;IAElD,MAAMwB,aAAa,GAAGC,IAAAA,KAAoB,EAAA,qBAAA,EAAC;QAAEC,KAAK,EAAEH,eAAe;KAAE,CAAC,AAAC;IAEvE,6BAA6B;IAC7BH,UAAU,CAACO,GAAG,CAAC,CAACvB,GAAG,EAAEC,GAAG,EAAEuB,IAAI,GAAK;QACjC,qEAAqE;QAErE,0DAA0D;QAE1DvB,GAAG,CAACwB,SAAS,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QAClDxB,GAAG,CAACwB,SAAS,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC;QACpExB,GAAG,CAACwB,SAAS,CACX,8BAA8B,EAC9B,+DAA+D,CAChE,CAAC;QAEF,oCAAoC;QACpC,IAAIzB,GAAG,CAAC0B,MAAM,KAAK,SAAS,EAAE;YAC5BzB,GAAG,CAACW,UAAU,GAAG,GAAG,CAAC;YACrBX,GAAG,CAACY,GAAG,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QACDW,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;IAEHR,UAAU,CAACO,GAAG,CAAC,CAACvB,GAAG,EAAEC,GAAG,EAAEuB,IAAI,GAAK;QACjC,IAAI,CAACxB,CAAAA,GAAG,QAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEI,GAAG,CAAA,IAAKJ,GAAG,CAAC0B,MAAM,KAAK,KAAK,IAAI1B,GAAG,CAAC0B,MAAM,KAAK,MAAM,AAAC,EAAE;YAChE,OAAOF,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAMG,QAAQ,GAAGC,WAAW,CAAC5B,GAAG,CAACI,GAAG,CAAC,GAAG,IAAIyB,GAAG,CAAC7B,GAAG,CAACI,GAAG,CAAC,CAACuB,QAAQ,GAAG3B,GAAG,CAACI,GAAG,AAAC;QAC5E,IAAI,CAACuB,QAAQ,EAAE;YACb,OAAOH,IAAI,EAAE,CAAC;QAChB,CAAC;QAEDtD,KAAK,CAAC,CAAC,mBAAmB,CAAC,EAAEyD,QAAQ,CAAC,CAAC;QAEvC,MAAMG,MAAM,GAAGxB,IAAAA,KAAI,EAAA,QAAA,EAACN,GAAG,EAAE2B,QAAQ,EAAE;YACjCpB,IAAI,EAAEW,eAAe;YACrBa,UAAU,EAAE;gBAAC,MAAM;aAAC;SACrB,CAAC,AAAC;QAEH,oCAAoC;QACpC,IAAIC,YAAY,GAAG,KAAK,AAAC;QACzBF,MAAM,CAACrB,EAAE,CAAC,MAAM,EAAE,SAASwB,MAAM,GAAG;YAClC,gDAAgD;YAChDD,YAAY,GAAG,IAAI,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjBF,MAAM,CAACrB,EAAE,CAAC,OAAO,EAAE,SAASyB,KAAK,CAACxB,GAAQ,EAAE;YAC1C,IAAIsB,YAAY,IAAI,CAAC,CAACtB,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC,EAAE;gBAC3CY,IAAI,CAACd,GAAG,CAAC,CAAC;gBACV,OAAO;YACT,CAAC;YAEDc,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,OAAO;QACPM,MAAM,CAAChB,IAAI,CAACb,GAAG,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;IAEHe,UAAU,CAACO,GAAG,CAACH,aAAa,CAAC,CAAC;IAE9BJ,UAAU,CAACD,MAAM,CAAC1C,OAAO,CAACK,IAAI,CAAE,CAAC;AACnC,CAAC;AAED,SAASkD,WAAW,CAACxB,GAAW,EAAW;IACzC,IAAI;QACF,kCAAkC;QAClC,IAAIyB,GAAG,CAACzB,GAAG,CAAC,CAAC;QACb,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,eAAef,mBAAmB,CAACO,IAAY,EAAoB;IACjE,MAAMuC,UAAU,GAAGlD,KAAI,EAAA,QAAA,CAACC,IAAI,CAACU,IAAI,EAAE,CAAC,wBAAwB,CAAC,CAAC,AAAC;IAC/D,OAAO,CAAE,MAAMwC,IAAAA,IAAe,gBAAA,EAACD,UAAU,CAAC,AAAC,CAAC;AAC9C,CAAC"}