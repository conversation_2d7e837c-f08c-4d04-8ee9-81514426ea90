import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  View, Text, Image, TouchableOpacity, StyleSheet,
  Dimensions, Animated, PanResponder, Platform,
  ActivityIndicator, FlatList, Alert, ToastAndroid,
  Pressable, TextInput, SafeAreaView, ScrollView, Modal, StatusBar,
  TouchableWithoutFeedback, Easing, useWindowDimensions, RefreshControl
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons, FontAwesome, MaterialCommunityIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import TabHeader from '@/components/TabHeader';

import FilterOnboardingFlow, { FilterPreferences } from '@/components/FilterOnboarding/FilterOnboardingFlow';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Appearance } from 'react-native';
import { useAppContext, useSafeAppContext } from '@/context/AppContext';
import { supabase } from '@/lib/supabase';
import { recordSwipe, saveJobToBookmarks } from '@/services/swipeService';
import { applyToJob, updateApplicationStatus, fetchApplications, getAppliedJobIds } from '@/services/jobApplicationService';
import { getCoverLetterStatusMap, getEnhancedCoverLetterStatusMap } from '@/services/coverLetterService';
import { CoverLetterAttachmentModal } from '@/components/CoverLetterAttachmentModal';
import { CoverLetterStatusIndicator } from '@/components/CoverLetterStatusIndicator';
import { EnhancedAIJobEvaluationComponent } from '@/components/EnhancedAIJobEvaluation';
import { fetchJobs, fetchJobsForUser, fetchJobsWithPreferences, getUserLocation, searchJobs, markJobSwiped, getQueueStats } from '@/services/JobsService';
import { UpgradeModal } from '@/components/UpgradeModal';
import { SearchBar } from '@/components/SearchBar';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Badge } from '@/components/uiComponents';
import { fetchJobSearch } from '@/services/jobApiServices';
import { getUserJobPreferences, getPersonalizedJobRecommendations, JobRecommendation, JobMatchingResult, recordEnhancedSwipe, recordRecommendationInteraction } from '@/services/jobRecommendationService';
import { userDataService, UserMessage, UserApplication } from '@/services/userDataService';
// Removed old filter components - using new card-based filter system
// Removed location services - using simplified location in new filter system
import {
  JobFilters,
  defaultFilters,
  applyFilters,
  applyFiltersWithLocation,
  loadFilterSettings,
  saveFilterSettings
} from '@/services/jobFilterService';
import RenderHTML from 'react-native-render-html';
import { LightTheme, DarkTheme, ThemeColors } from '@/constants/Theme';
import { filterJobsForDisplay, validateJobForDisplay } from '../../utils/jobValidator';
// Removed ApiDebugger component - consolidated into main debugging tools
import { router } from 'expo-router';
import { Job as ContextJob, AppliedJob } from '@/context/AppContext';
import { useAuthCheck } from '@/utils/authUtils';

const SCREEN_WIDTH = Dimensions.get('window').width;
const SCREEN_HEIGHT = Dimensions.get('window').height;
const SWIPE_THRESHOLD = SCREEN_WIDTH * 0.25;
const CARD_HEIGHT = SCREEN_HEIGHT * 0.65; // Reduced from 0.75 to avoid overlapping buttons in default state

// Simple function to prevent errors if we've gone past array bounds
const getCardIndex = (index: number, cards: any[]): number => {
  // If we've swiped through all cards, go back to the first
  if (index >= cards.length) {
    return 0;
  }
  return index;
};

// No longer needed as timeline is removed

interface Job {
  aiExplanation?: string;
  aiEvaluation?: any; // AI evaluation data including score, strengths, concerns, etc.
  id: string;
  title: string;
  company: string;
  location: string;
  pay: string;
  image: string;
  logo?: string; // Optional logo URL for backward compatibility
  distance: string;
  tags: string[];
  description: string;
  qualifications: string[];
  requirements: string[];
  type?: string; // Job type (full-time, part-time, contract, etc.)
  experienceLevel?: string; // Experience level required
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  skills?: string[]; // Required skills
}

interface Application {
  id: string;
  jobTitle: string;
  company: string;
  logo: string;
  appliedAt: string;
  status: string;
  statusColor: string;
  job?: Job; // Optional reference to the full job
}

interface Message {
  id: string;
  contact: string;
  company: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: boolean;
}

// Mock jobs removed - jobs are now fetched from external APIs and Supabase

// JobsScreenWrapper component to safely handle context
const JobsScreenWrapper = () => {
  // Enforce authentication
  useAuthCheck();

  // Pre-extract the context values here - separating context use from render
  const context = useAppContext();

  // Simplified to only browse jobs - removed inbox functionality
  const [activeTab] = useState<'browse'>('browse');

  // Use separate state for applications to ensure reactivity
  const [localApplications, setLocalApplications] = useState<AppliedJob[]>([]);
  const [coverLetterStatusMap, setCoverLetterStatusMap] = useState<Record<string, boolean>>({});

  // Cover letter attachment modal state
  const [coverLetterModalVisible, setCoverLetterModalVisible] = useState(false);
  const [selectedJobForCoverLetter, setSelectedJobForCoverLetter] = useState<Job | null>(null);

  // Handle opening cover letter modal
  const openCoverLetterModal = (job: Job) => {
    setSelectedJobForCoverLetter(job);
    setCoverLetterModalVisible(true);
  };

  // Handle closing cover letter modal
  const closeCoverLetterModal = () => {
    setCoverLetterModalVisible(false);
    setSelectedJobForCoverLetter(null);
  };

  // Handle cover letter attachment change
  const handleCoverLetterAttachmentChange = async (hasAttachment: boolean) => {
    if (selectedJobForCoverLetter && context.user?.id) {
      // Update the cover letter status map
      setCoverLetterStatusMap(prev => ({
        ...prev,
        [selectedJobForCoverLetter.id]: hasAttachment
      }));

      // Refresh the cover letter status map from the server
      try {
        const statusMap = await getCoverLetterStatusMap(context.user.id);
        setCoverLetterStatusMap(statusMap);
      } catch (error) {
        console.error('Error refreshing cover letter status:', error);
      }
    }
  };

  // Initialize applications from context
  useEffect(() => {
    setLocalApplications(context.applications);
  }, [context.applications]);

  // Update applications when we switch to inbox tab
  useEffect(() => {
    if (activeTab === 'browse') { // Fixed: changed 'inbox' to 'browse' to match the type
      console.log('[DEBUG] Inbox tab active, fetching applications');

      // First, get applications from context
      console.log(`[DEBUG] Using ${context.applications.length} applications from context`);
      console.log('[DEBUG] Context applications:', context.applications.map(app =>
        `${app.job.id} (${app.job.title})`));

      // Then try to fetch from backend
      if (context.user) {
        Promise.all([
          fetchApplications(context.user.id),
          getCoverLetterStatusMap(context.user.id)
        ])
          .then(([backendApplications, coverLetterMap]) => {
            console.log(`[DEBUG] Fetched ${backendApplications.length} applications from backend`);
            console.log('[DEBUG] Backend applications:', backendApplications.map(app =>
              `${app.job.id} (${app.job.title})`));

            // Set cover letter status
            setCoverLetterStatusMap(coverLetterMap);

            // Start with the applications from context
            const allApplications = [...context.applications];

            // Add new backend applications that aren't in context
            let newApplicationsCount = 0;
            backendApplications.forEach(backendApp => {
              // Check if this application already exists in context by job ID
              const existingAppIndex = allApplications.findIndex(
                contextApp => contextApp.job.id === backendApp.job.id
              );

              if (existingAppIndex === -1) {
                // This is a new application not in context, add it
                allApplications.push(backendApp);
                // Also add to global context for persistence
                context.addApplication(backendApp.job);
                newApplicationsCount++;
                console.log(`[DEBUG] Adding new application from backend: ${backendApp.job.title}`);
              } else {
                // This application exists in context, update its status if needed
                if (allApplications[existingAppIndex].status !== backendApp.status) {
                  console.log(`[DEBUG] Updating application status for ${backendApp.job.title} from ${allApplications[existingAppIndex].status} to ${backendApp.status}`);
                  allApplications[existingAppIndex].status = backendApp.status;
                  allApplications[existingAppIndex].statusColor = backendApp.statusColor;
                }
              }
            });

            console.log(`[DEBUG] Added ${newApplicationsCount} new applications from backend`);
            console.log(`[DEBUG] Total unique applications after merge: ${allApplications.length}`);

            // Log all final applications for debugging
            allApplications.forEach((app, index) => {
              console.log(`[DEBUG] Application ${index+1}: ID=${app.job.id}, Title=${app.job.title}, Status=${app.status}`);
            });

            // Update local state with all applications
            setLocalApplications(allApplications);
          })
          .catch(error => {
            console.error('[ERROR] Failed to fetch applications from backend:', error);
            // On error, at least show the context applications
            setLocalApplications([...context.applications]);
          });
      } else {
        // No user logged in, just use context applications
        setLocalApplications([...context.applications]);
      }
    }
  }, [activeTab, context.user, context.applications, context.addApplication]);

  // Create a wrapper for addApplication that updates local state immediately
  const handleAddApplication = useCallback((job: Job) => {
    console.log('[DEBUG] Adding application for job:', job.id, job.title);

    // First call the context method to persist to storage
    context.addApplication(job);

    // Then update local state immediately for UI responsiveness
    setLocalApplications(prevApps => {
      // Check if this job is already in applications
      if (prevApps.some(app => app.job.id === job.id)) {
        console.log('[DEBUG] Application already exists, not adding duplicate');
        return prevApps;
      }

      console.log('[DEBUG] Adding new application to local state');

      // Add the new application to the beginning of the list
      const newApps = [{
        job,
        status: 'applying' as const, // Fix the TypeScript error by using "as const"
        statusColor: '#FFC107',
        appliedAt: new Date().toISOString(),
      }, ...prevApps];

      console.log(`[DEBUG] Local applications count is now: ${newApps.length}`);
      return newApps;
    });
  }, [context.addApplication]);

  // Provide pre-extracted values as props, but use local applications state
  return (
    <>
      <JobsScreen
        theme={context.theme}
        addApplication={handleAddApplication}
        userApplications={localApplications}
        coverLetterStatusMap={coverLetterStatusMap}
        user={context.user}
        // Removed activeMainTab props - simplified to browse only
        openCoverLetterModal={openCoverLetterModal}
      />

      {/* Cover Letter Attachment Modal */}
      <CoverLetterAttachmentModal
        visible={coverLetterModalVisible}
        onClose={closeCoverLetterModal}
        jobId={selectedJobForCoverLetter?.id || ''}
        userId={context.user?.id || ''}
        jobTitle={selectedJobForCoverLetter?.title || ''}
        companyName={selectedJobForCoverLetter?.company || ''}
        themeColors={context.theme === 'light' ? LightTheme : DarkTheme}
        onAttachmentChange={handleCoverLetterAttachmentChange}
      />
    </>
  );
};

// Modified JobsScreen to receive props instead of using context directly
// Global cache to track failed logo URLs to avoid repeated attempts
const failedLogosCache = new Set<string>();

// Smart logo component that handles fallbacks (moved outside to prevent hooks order issues)
// Generate gradient colors based on company name
const getCompanyColors = (company: string) => {
  const colorSets = [
    ['#667eea', '#764ba2'], // Purple-blue
    ['#f093fb', '#f5576c'], // Pink-red
    ['#4facfe', '#00f2fe'], // Blue-cyan
    ['#43e97b', '#38f9d7'], // Green-cyan
    ['#fa709a', '#fee140'], // Pink-yellow
    ['#a8edea', '#fed6e3'], // Cyan-pink
    ['#ff9a9e', '#fecfef'], // Pink-purple
    ['#ffecd2', '#fcb69f'], // Yellow-orange
  ];
  const index = company.length % colorSets.length;
  return colorSets[index];
};

const SmartLogo: React.FC<{
  company: string | { display_name: string };
  logoUrl: string;
  style: any;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  jobTitle?: string;
}> = React.memo(({ company, logoUrl, style, resizeMode = 'contain', jobTitle }) => {
  // Ensure company is a string
  const companyName = typeof company === 'string' ? company :
    (company && typeof company === 'object' && company.display_name) ? company.display_name : 'Unknown Company';

  // Check if this logo has already failed or is likely to fail
  const isKnownFailure = failedLogosCache.has(logoUrl);
  const isClearbitUrl = logoUrl && logoUrl.includes('logo.clearbit.com');
  const hasNoLogo = !logoUrl || logoUrl.trim() === '' || logoUrl.includes('placeholder') || logoUrl.includes('no-logo');
  const [hasError, setHasError] = React.useState(isKnownFailure || isClearbitUrl || hasNoLogo);
  const [showFallback, setShowFallback] = React.useState(isKnownFailure || isClearbitUrl || hasNoLogo);

  React.useEffect(() => {
    const isKnownFailure = failedLogosCache.has(logoUrl);
    const isClearbitUrl = logoUrl && logoUrl.includes('logo.clearbit.com');
    const hasNoLogo = !logoUrl || logoUrl.trim() === '' || logoUrl.includes('placeholder') || logoUrl.includes('no-logo');
    setHasError(isKnownFailure || isClearbitUrl || hasNoLogo);
    setShowFallback(isKnownFailure || isClearbitUrl || hasNoLogo);
  }, [logoUrl, companyName]);

  const handleError = React.useCallback(() => {
    if (!hasError && !failedLogosCache.has(logoUrl)) {
      console.log(`Logo failed for ${companyName}, showing job title`);
      setHasError(true);
      setShowFallback(true);
      failedLogosCache.add(logoUrl);
    }
  }, [hasError, companyName, logoUrl]);

  // If we should show fallback, render job title in large text without image placeholder
  if (showFallback) {
    // Prioritize job title over company name for display
    const displayText = jobTitle || companyName;
    // Calculate responsive font size based on container dimensions
    const fontSize = Math.min(style.width / 6, style.height / 3, 24);
    
    return (
      <View style={[style, {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'transparent', // Remove background to eliminate placeholder
        borderRadius: style.borderRadius || 12,
        overflow: 'hidden',
        padding: 16
      }]}>
        <Text style={{
          color: '#2c3e50',
          fontSize: fontSize,
          fontWeight: '700',
          textAlign: 'center',
          paddingHorizontal: 8,
          lineHeight: fontSize * 1.2,
          letterSpacing: 0.5
        }} numberOfLines={3} adjustsFontSizeToFit>
          {displayText}
        </Text>
      </View>
    );
  }

  // Show actual logo if available
  return (
    <Image
      source={{ uri: logoUrl }}
      style={style}
      resizeMode={resizeMode}
      onError={handleError}
    />
  );
});

const JobsScreen: React.FC<{
  theme: 'light' | 'dark',
  addApplication: (job: Job) => void,
  userApplications: AppliedJob[],
  coverLetterStatusMap: Record<string, boolean>,
  user: any,
  openCoverLetterModal: (job: Job) => void
}> = ({
  theme,
  addApplication,
  userApplications,
  coverLetterStatusMap,
  user,
  openCoverLetterModal
}) => {  // Using React.FC to explicitly type the component
  // Use theme-derived values in a separate memo to avoid render-time calculations
  const themeColors = React.useMemo(() => {
    return theme === 'light' ? LightTheme : DarkTheme;
  }, [theme]);

  // Enhanced theme-aware colors for better visibility
  const redColor = React.useMemo(() => theme === 'light' ? '#FF3B30' : '#FF5252', [theme]);
  const greenColor = React.useMemo(() => theme === 'light' ? '#4CD964' : '#4CAF50', [theme]);
  const blueColor = React.useMemo(() => theme === 'light' ? '#2196F3' : '#42A5F5', [theme]);
  const overlayIconColor = React.useMemo(() => theme === 'light' ? '#000' : '#fff', [theme]);

  // Enhanced icon colors for better contrast in both themes
  const iconColorInactive = React.useMemo(() => theme === 'light' ? '#666666' : '#ffffff', [theme]);
  const iconColorActive = React.useMemo(() => theme === 'light' ? '#ffffff' : '#222222', [theme]);
  const insets = useSafeAreaInsets();
  const windowWidth = useWindowDimensions().width;

  // Current swipe position for visual feedback during gesture
  const [swipePosition, setSwipePosition] = useState({ x: 0, y: 0 });
  // Whether the card is currently being swiped
  const [isSwiping, setIsSwiping] = useState(false);
  // Animation completion states
  const [hasSwipedLeft, setHasSwipedLeft] = useState(false);
  const [hasSwipedRight, setHasSwipedRight] = useState(false);

  // Don't need these anymore
  const swipeAnim = useRef(new Animated.ValueXY()).current;
  const [swipeOverlay, setSwipeOverlay] = useState<'like' | 'save' | 'pass' | 'info' | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [swipeTintColor, setSwipeTintColor] = useState<'none' | 'green' | 'red' | 'blue'>('none');
  
  // Debounce mechanism to prevent multiple rapid swipes
  const lastSwipeTime = useRef(0);
  const SWIPE_DEBOUNCE_MS = 500; // Prevent multiple swipes within 500ms

  // Progress stats
  const [jobsApplied, setJobsApplied] = useState(0);
  const [jobsTarget] = useState(12);
  const [progressPercent, setProgressPercent] = useState(0);

  // Removed inbox functionality - simplified to browse only

  // Jobs state
  const [jobs, setJobs] = useState<Job[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [coverLetterStatus, setCoverLetterStatus] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [queueStats, setQueueStats] = useState<any>(null);

  // Enhanced features state
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeInfo, setUpgradeInfo] = useState({ searchesUsed: 0, searchLimit: 5 });
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  // Removed old refine filters state - using new filter system

  // State for personalized recommendations
  const [recommendations, setRecommendations] = useState<JobRecommendation[]>([]);
  const [matchingResult, setMatchingResult] = useState<JobMatchingResult | null>(null);
  const [hasUserPreferences, setHasUserPreferences] = useState(false);
  const [showRecommendationInfo, setShowRecommendationInfo] = useState(false);
  const [isJobDetailsVisible, setIsJobDetailsVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isViewingFromInbox, setIsViewingFromInbox] = useState(false);

  // Animation and button states
  const [undoActive, setUndoActive] = useState(false);
  const [passActive, setPassActive] = useState(false);
  const [infoActive, setInfoActive] = useState(false);
  const [likeActive, setLikeActive] = useState(false);
  const likeScale = useRef(new Animated.Value(1)).current;
  const passScale = useRef(new Animated.Value(1)).current;
  const infoScale = useRef(new Animated.Value(1)).current;
  const [swipedJobs, setSwipedJobs] = useState<{job: Job, direction: 'left' | 'right', index: number}[]>([]);

  // Safe current job reference - get the current job with error checking
  const safeCurrentJob = React.useMemo(() => {
    return filteredJobs[currentIndex] || null;
  }, [filteredJobs, currentIndex]);

  // Enhanced Filter state (simplified for new filter system)
  const [jobFilters, setJobFilters] = useState<JobFilters>(defaultFilters);
  const [userLocation, setUserLocation] = useState<string>('');

  // Removed legacy filter state - using card-based filters only

  // Removed message-related state variables - simplified to browse only

  // Restore showAppliedNotification and setShowAppliedNotification state
  const [showAppliedNotification, setShowAppliedNotification] = useState(false);
  // Restore isSearchVisible and setIsSearchVisible state
  const [isSearchVisible, setIsSearchVisible] = useState(false);

  // Removed old filter UI - using card-based filters only

  // Add state for API debugger visibility

  
  // Add state for filter onboarding - start with false to show onboarding by default
  const [hasCompletedFilters, setHasCompletedFilters] = useState(false);
  const [filterPreferences, setFilterPreferences] = useState<FilterPreferences | null>(null);

  // --- Animated Filter Panel ---
  const filterAnim = useRef(new Animated.Value(0)).current;

  // Removed toggleFilters - using card-based filters only

  // Show a notification message
  const showNotification = (message: string) => {
    console.log(message);
    // Set notification state and auto-hide after a delay
    setShowAppliedNotification(true);
    setTimeout(() => {
      setShowAppliedNotification(false);
    }, 2000);
  };

  // Handle filter onboarding completion
  const handleFilterOnboardingComplete = async (preferences: FilterPreferences) => {
    try {
      console.log('[ONBOARDING] Completing filter onboarding with preferences:', preferences);

      // Save preferences to AsyncStorage (user-specific)
      await AsyncStorage.setItem(`filterPreferences_${user.id}`, JSON.stringify(preferences));
      setFilterPreferences(preferences);
      setHasCompletedFilters(true);

      // Save user job preferences to backend
      if (user?.id) {
        try {
          const userPreferences = {
            user_id: user.id,
            preferred_locations: [preferences.location],
            max_commute_distance: 50,
            remote_work_preference: preferences.workStyle === 'remote' ? 'required' :
                                   preferences.workStyle === 'hybrid' ? 'preferred' : 'acceptable',
            willing_to_relocate: false,
            preferred_job_types: preferences.jobTypes,
            preferred_industries: [],
            preferred_company_sizes: [],
            experience_level: preferences.experienceLevel || 'mid',
            preferred_roles: preferences.jobCategories,
            min_salary: preferences.salaryRange.min,
            max_salary: preferences.salaryRange.max,
            salary_currency: 'USD',
            salary_negotiable: true,
            preferred_schedule: 'flexible',
            location_weight: 0.25,
            salary_weight: 0.3,
            role_weight: 0.25,
            company_weight: 0.2,
            auto_learn_from_swipes: true,
            plan: 'free',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          console.log('[ONBOARDING] Saving user preferences to backend:', userPreferences);

          // Save to Supabase
          const { data, error } = await supabase
            .from('user_job_preferences')
            .upsert(userPreferences)
            .select();

          if (error) {
            console.error('[ONBOARDING] Error saving user preferences:', error);
          } else {
            console.log('[ONBOARDING] Successfully saved user preferences:', data);
          }
        } catch (backendError) {
          console.error('[ONBOARDING] Error saving to backend:', backendError);
        }
      }

      // Update job filters based on preferences (more lenient filtering)
      const updatedFilters: JobFilters = {
        ...defaultFilters,
        location: preferences.location,
        remoteOnly: preferences.workStyle === 'remote',
        inPersonOnly: preferences.workStyle === 'in-person',
        employmentTypes: preferences.jobTypes.length > 0 ? preferences.jobTypes : [], // Allow all if none selected
        experienceLevel: preferences.experienceLevel ? [preferences.experienceLevel] : [],
        minSalary: 0, // More lenient salary filtering
        maxSalary: 500000,
        jobCategories: [], // Don't filter by categories initially to show more jobs
        hasLogo: false, // Don't require logos
        quickFilters: {
          remote: preferences.workStyle === 'remote',
          recentlyPosted: false,
          hasLogo: false
        }
      };

      console.log('[ONBOARDING] Setting job filters:', updatedFilters);
      setJobFilters(updatedFilters);

      // Save filter settings if user is available
      if (user?.id) {
        try {
          await saveFilterSettings(user.id, updatedFilters);
          console.log('[ONBOARDING] Saved filter settings to database');
        } catch (saveError) {
          console.error('[ONBOARDING] Error saving filter settings:', saveError);
        }
      }

      // Load jobs immediately without delay
      console.log('[ONBOARDING] Loading jobs with new filters...');
      await loadJobs(true); // Force reset to get fresh jobs

    } catch (error) {
      console.error('[ONBOARDING] Error completing filter onboarding:', error);
      // Fallback: just mark as completed and load basic jobs
      setHasCompletedFilters(true);
      await loadJobs().catch(err => {
        console.error('[ONBOARDING] Fallback job loading failed:', err);
        setIsLoading(false);
      });
    }
  };

  // Skip filter onboarding (show basic jobs)
  const handleSkipFilterOnboarding = () => {
    try {
      console.log('[ONBOARDING] Skipping filter onboarding');
      setHasCompletedFilters(true);
      setTimeout(() => {
        loadJobs().catch(error => {
          console.error('[ONBOARDING] Error loading jobs after skip:', error);
          setIsLoading(false);
        });
      }, 100);
    } catch (error) {
      console.error('[ONBOARDING] Error in handleSkipFilterOnboarding:', error);
      setHasCompletedFilters(true);
      setIsLoading(false);
    }
  };

  // Temporary function to clear AsyncStorage and force filter onboarding
  const clearUserPreferences = async () => {
    if (user?.id) {
      try {
        await AsyncStorage.removeItem(`filterPreferences_${user.id}`);
        console.log('[DEBUG] Cleared user preferences from AsyncStorage');
        setHasCompletedFilters(false);
        setFilterPreferences(null);
      } catch (error) {
        console.error('[DEBUG] Error clearing preferences:', error);
      }
    }
  };

  // Debug function to reset animation state if it gets stuck
  const resetAnimationState = () => {
    console.log('[DEBUG] Manually resetting animation state');
    setIsAnimating(false);
    setSwipeOverlay(null);
    setSwipeTintColor('none');
    setSwipePosition({ x: 0, y: 0 });
    swipeAnim.setValue({ x: 0, y: 0 });
    setIsSwiping(false);
  };

  // Animate button press effect
  const animateButton = (scaleAnim: Animated.Value, toValue: number) => {
    Animated.spring(scaleAnim, {
      toValue,
      friction: 5,
      tension: 40,
      useNativeDriver: true
    }).start();
  };

  // Complete a swipe animation with enhanced effects
  const completeSwipeAnimation = (direction: 'left' | 'right') => {
    if (!safeCurrentJob) return;

    // Set animation state to prevent multiple swipes at once
    setIsAnimating(true);

    // Record the swipe in our state
    setSwipedJobs(prev => [...prev, { job: safeCurrentJob, direction, index: currentIndex }]);

    // Enhanced animation with rotation, scaling, and improved visual feedback
    const xPosition = direction === 'left' ? -SCREEN_WIDTH * 1.8 : SCREEN_WIDTH * 1.8;
    const yPosition = direction === 'left' ? -150 : -80; // Different trajectories for left/right
    const rotationValue = direction === 'left' ? -35 : 35; // Degrees for more dramatic rotation
    
    // Enhanced parallel animations with spring physics and easing
    Animated.parallel([
      // Main card movement with spring physics
      Animated.spring(swipeAnim, {
        toValue: { x: xPosition, y: yPosition },
        tension: 65,
        friction: 8,
        useNativeDriver: true,
      }),
      // Rotation animation with easing
      Animated.timing(swipeAnim.x, {
        toValue: xPosition,
        duration: 450,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      // Scale animation for depth effect
      Animated.timing(new Animated.Value(1), {
        toValue: 0.7,
        duration: 450,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      // Opacity fade for smooth exit
      Animated.timing(new Animated.Value(1), {
        toValue: 0,
        duration: 300,
        delay: 150,
        useNativeDriver: true,
      })
    ]).start(() => {
      // Reset animation position
      swipeAnim.setValue({ x: 0, y: 0 });
      setSwipePosition({ x: 0, y: 0 });
      
      // Permanently remove this job from the filtered jobs array to prevent it from reappearing
      setFilteredJobs(currentJobs => {
        return currentJobs.filter(job => job.id !== safeCurrentJob.id);
      });

      // Clean up the animation state
      setIsAnimating(false);
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    });
  };

  // Handle left swipe (pass)
  const handleSwipeLeft = useCallback(async () => {
    if (!safeCurrentJob || isAnimating) {
      console.log('Swipe left blocked - no job or already animating');
      return;
    }

    // Set animating state immediately to prevent concurrent swipes
    setIsAnimating(true);
    console.log('Starting swipe left animation and recording');

    // Close modal if open to ensure smooth transition
    if (isJobDetailsVisible) {
      closeJobDetailsWithAnimation();
    }

    // Log the job being swiped left
    console.log('Swiping job left to pass:', safeCurrentJob.id, safeCurrentJob.title);

    // Store job reference to avoid race conditions
    const jobToSwipe = { ...safeCurrentJob };
    const currentIndexSnapshot = currentIndex;

    // Add to swipedJobs array to enable undo functionality
    setSwipedJobs(prev => {
      const newSwipedJobs = [...prev, { job: jobToSwipe, direction: 'left' as 'left', index: currentIndexSnapshot }];

      // Save swiped job IDs to AsyncStorage for persistence
      // Load existing IDs first, then add the new one
      loadSwipedJobIds().then(existingIds => {
        const updatedIds = [...existingIds, jobToSwipe.id];
        saveSwipedJobIds(updatedIds);
      }).catch(err => console.error('Error updating swiped job IDs:', err));

      // Also mark as swiped in the enhanced queue system
      if (user?.id) {
        markJobSwiped(user.id, jobToSwipe.id).catch(err =>
          console.error('Error marking job swiped in backend:', err)
        );

        // Proactively fetch more jobs if we're running low (15 or fewer left)
        const remainingJobs = filteredJobs.length - currentIndex - 1;
        if (remainingJobs <= 15 && !isLoadingJobsRef.current) {
          console.log(`Proactively fetching more jobs (${remainingJobs} remaining)...`);
          loadJobs();
        }
      }

      return newSwipedJobs;
    });
    console.log('Job added to swipedJobs array and saved to AsyncStorage');

    // Permanently remove this job from the filtered jobs array to prevent it from reappearing
    setFilteredJobs(prev => prev.filter(job => job.id !== jobToSwipe.id));
    console.log('Removed job from filtered jobs to prevent reappearing');

    // Record the enhanced swipe with job details for learning
    if (user) {
      // Get the match score for this job if available
      const recommendation = recommendations.find(rec => rec.job.id === jobToSwipe.id);
      const matchScore = recommendation?.overall_score;

      recordEnhancedSwipe(user.id, jobToSwipe, 'left', matchScore)
        .then(() => {
          console.log('Enhanced swipe recorded for learning algorithm');
          // Also record the interaction for analytics
          return recordRecommendationInteraction(user.id, jobToSwipe.id, {
            was_viewed: true,
            was_swiped: true,
            swipe_direction: 'left'
          });
        })
        .catch((err: Error) => {
          console.error('Failed to record enhanced swipe:', err);
          // Fallback to basic swipe recording
          return recordSwipe(jobToSwipe.id, user.id, 'dislike');
        })
        .catch((fallbackErr: Error) => {
          console.error('Failed to record fallback swipe:', fallbackErr);
        });
    }

    // Set swiped state to trigger visual effect
    setHasSwipedLeft(true);

    // Move to next card after a delay
    setTimeout(() => {
      goToNextCard();

      // Reset animation state
      setIsAnimating(false);
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    }, 300);
  }, [safeCurrentJob, user, currentIndex, isAnimating]);

  // Handle undo of previous swipe
  const handleUndoSwipe = async () => {
    // Don't do anything if no jobs to restore or already animating
    if (swipedJobs.length === 0 || isAnimating) return;

    console.log('Undoing swipe, current swiped jobs:', swipedJobs.length);

    // Get the last swiped job
    const lastSwipedJob = swipedJobs[swipedJobs.length - 1];

    // Reset all visual states
    setIsAnimating(true);
    setHasSwipedLeft(false);
    setHasSwipedRight(false);
    setSwipePosition({ x: 0, y: 0 });
    swipeAnim.setValue({ x: 0, y: 0 });

    // If it was a right swipe (like), we'd need to remove it from applications
    if (lastSwipedJob.direction === 'right') {
      console.log('Removing job from saved list:', lastSwipedJob.job.title);
      // In a real app with a backend, remove it from saved jobs
    }

    // Add the job back to our filtered jobs array
    setFilteredJobs(currentJobs => {
      // Create a new array with the job inserted at the current index
      const newJobs = [...currentJobs];
      newJobs.splice(currentIndex, 0, lastSwipedJob.job);
      return newJobs;
    });

    // Remove from swiped jobs history in memory
    setSwipedJobs(prev => prev.slice(0, -1));

    // Remove the job ID from AsyncStorage as well
    try {
      // Get current stored job IDs
      const storedIds = await loadSwipedJobIds();
      // Remove the undone job ID
      const updatedIds = storedIds.filter(id => id !== lastSwipedJob.job.id);
      // Save the updated list back to AsyncStorage
      await saveSwipedJobIds(updatedIds);
      console.log('Removed job ID from persisted storage during undo');
    } catch (error) {
      console.error('Failed to update AsyncStorage during undo:', error);
    }

    // Show feedback to user
    showNotification('Brought back previous job');

    // Clean up animation state after a delay
    setTimeout(() => {
      setIsAnimating(false);
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    }, 300);
  };



  // Create a test job application
  const createTestJobApplication = () => {
    if (!user) return;

    // Use a job from the actual jobs data if available, otherwise use mock job
    let testJob = null;

    // Try to find a job from the fetched jobs first
    if (jobs && jobs.length > 0) {
      testJob = jobs[0];
    }

    if (!testJob) {
      console.log('No jobs available for test application');
      return;
    }

    // Check if we already have an application for this job
    if (userApplications.some(app => app.job.id === testJob.id)) {
      console.log('[DEBUG] Application already exists, not adding duplicate');
      return;
    }

    // Add to context
    addApplication(testJob);

    // Log for debugging
    console.log('Created test application for:', testJob.title);
  };

  // State for Tinder-like expandable job details
  const [detailsExpanded, setDetailsExpanded] = useState(false);
  const detailsAnimation = useRef(new Animated.Value(0)).current;

  // Enhanced modal animation values for smooth slide-up/down animations
  const modalSlideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current; // Start off-screen
  const backdropOpacityAnim = useRef(new Animated.Value(0)).current;
  const modalDragY = useRef(new Animated.Value(0)).current; // For gesture handling
  const [isDragging, setIsDragging] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // Debug helper to track interactions
  const logInteraction = (event: string, data: Record<string, any>) => {
    console.log(`Panel interaction: ${event}`, data);
  };

  // Pan responder for modal gesture handling
  const modalPanResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Only respond to vertical gestures and when not already dragging horizontally
        return Math.abs(gestureState.dy) > Math.abs(gestureState.dx) && Math.abs(gestureState.dy) > 10;
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
        // Use current animated value via type cast to any to avoid private property errors
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        modalDragY.setOffset((modalDragY as any)._value);
        modalDragY.setValue(0);
      },
      onPanResponderMove: (evt, gestureState) => {
        // Only allow downward dragging
        if (gestureState.dy > 0) {
          modalDragY.setValue(gestureState.dy);
          // Update backdrop opacity based on drag distance
          const dragProgress = Math.min(gestureState.dy / 200, 1);
          backdropOpacityAnim.setValue(0.7 * (1 - dragProgress));
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        setIsDragging(false);
        modalDragY.flattenOffset();

        // If dragged down more than 100px or with sufficient velocity, close modal
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          closeJobDetailsWithAnimation();
        } else {
          // Snap back to original position
          Animated.parallel([
            Animated.spring(modalDragY, {
              toValue: 0,
              useNativeDriver: true,
              tension: 100,
              friction: 8,
            }),
            Animated.timing(backdropOpacityAnim, {
              toValue: 0.7,
              duration: 200,
              useNativeDriver: false,
            }),
          ]).start();
        }
      },
    })
  ).current;

  // Improved pan responder with better touch handling
  const detailsPanResponder = useRef(
    PanResponder.create({
      // Always grant the responder to this component
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,

      // For movement-based interactions
      onMoveShouldSetPanResponder: (_, gesture) => {
        logInteraction('onMoveShouldSetPanResponder', { dx: gesture.dx, dy: gesture.dy });
        // Less strict gesture detection
        return Math.abs(gesture.dy) > 5;
      },
      onMoveShouldSetPanResponderCapture: () => false,

      // Handle active gestures
      onPanResponderGrant: () => {
        logInteraction('onPanResponderGrant', {});
      },

      onPanResponderMove: (_, gesture) => {
        logInteraction('onPanResponderMove', { dy: gesture.dy });
        // Allow all vertical movement for more fluid interaction
        const newValue = detailsExpanded ?
          1 + (gesture.dy / (SCREEN_HEIGHT * 0.6)) : // When expanded, start from 1 and go down
          (gesture.dy / -(SCREEN_HEIGHT * 0.6));    // When collapsed, start from 0 and go up

        // Clamp values between 0 and 1 with less restriction
        const clampedValue = Math.min(Math.max(newValue, 0), 1);
        detailsAnimation.setValue(clampedValue);
      },

      onPanResponderRelease: (_, gesture) => {
        logInteraction('onPanResponderRelease', { dy: gesture.dy, vy: gesture.vy });
        // Use velocity and distance for more natural interaction
        if (detailsExpanded) {
          // When expanded, determine if we should collapse
          if (gesture.dy > 50 || gesture.vy > 0.3) {
            collapseJobDetails();
          } else {
            // Snap back to fully expanded
            Animated.spring(detailsAnimation, {
              toValue: 1,
              useNativeDriver: false, // Can't use native driver with layout properties
              tension: 50,
              friction: 7
            }).start();
          }
        } else {
          // When collapsed, determine if we should expand
          if (gesture.dy < -50 || gesture.vy < -0.3) {
            expandJobDetails();
          } else {
            // Snap back to collapsed
            Animated.spring(detailsAnimation, {
              toValue: 0,
              useNativeDriver: false, // Can't use native driver with layout properties
              tension: 50,
              friction: 7
            }).start();
          }
        }
      },

      // Ensure we release the responder properly
      onPanResponderTerminationRequest: () => true,
      onPanResponderTerminate: () => {
        logInteraction('onPanResponderTerminate', {});
      }
    })
  ).current;

  // Enhanced openJobDetails with smooth slide-up animation
  const openJobDetails = (job: Job, fromInbox: boolean = false) => {
    // Set the selected job first
    setSelectedJob(job);

    // Store context of where this was opened from
    setIsViewingFromInbox(fromInbox);

    // Reset all animation values to ensure starting fresh
    modalSlideAnim.setValue(SCREEN_HEIGHT);
    backdropOpacityAnim.setValue(0);
    modalDragY.setValue(0);
    setIsDragging(false);

    // Make the modal visible
    setModalVisible(true);
    setIsJobDetailsVisible(true);

    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      // Smooth slide-up animation with backdrop fade-in
      Animated.parallel([
        Animated.timing(modalSlideAnim, {
          toValue: 0,
          duration: 350,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacityAnim, {
          toValue: 0.7,
          duration: 300,
          easing: Easing.out(Easing.quad),
          useNativeDriver: false,
        }),
      ]).start();
    });
  };

  // Expand job details with animation
  const expandJobDetails = () => {
    console.log('Expanding job details');
    setDetailsExpanded(true);
    Animated.spring(detailsAnimation, {
      toValue: 1,
      useNativeDriver: false, // Can't use native driver with layout properties
      tension: 50,
      friction: 7
    }).start(() => {
      console.log('Expand animation completed');
    });
  };

  // Collapse job details with animation
  const collapseJobDetails = () => {
    console.log('Collapsing job details');
    setDetailsExpanded(false);
    Animated.spring(detailsAnimation, {
      toValue: 0,
      useNativeDriver: false, // Can't use native driver with layout properties
      tension: 80,
      friction: 7
    }).start(() => {
      console.log('Collapse animation completed');
      // Only close the modal if we're not already expanded again
      if (!detailsExpanded) {
        setIsJobDetailsVisible(false);
      }
    });
  };

  // Enhanced close function with smooth slide-down animation
  const closeJobDetailsWithAnimation = () => {
    console.log('Closing job details with animation');

    // Smooth slide-down animation with backdrop fade-out
    Animated.parallel([
      Animated.timing(modalSlideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacityAnim, {
        toValue: 0,
        duration: 250,
        easing: Easing.in(Easing.quad),
        useNativeDriver: false,
      }),
    ]).start(() => {
      // Reset state after animation completes
      setModalVisible(false);
      setIsJobDetailsVisible(false);
      setIsViewingFromInbox(false);
      setDetailsExpanded(false);
      modalDragY.setValue(0);
      setIsDragging(false);
    });
  };

  // Close job details modal (legacy function for compatibility)
  const closeJobDetails = () => {
    closeJobDetailsWithAnimation();
  };

  // Manual toggle for when gestures fail
  const toggleJobDetails = () => {
    console.log('Manually toggling job details');
    if (detailsExpanded) {
      collapseJobDetails();
    } else {
      expandJobDetails();
    }
  };

  // Improved touch handlers for visual feedback during swipe
  const handleTouchStart = useRef({ x: 0, y: 0 });

  // Track if this was a tap or a swipe
  const isTap = useRef(true);

  const onTouchStart = (e: any) => {
    // Reset tap detection on touch start
    isTap.current = true;

    // Debug log for animation state
    console.log('Touch start - isAnimating:', isAnimating);
    
    // Safety reset: if animation has been stuck for too long, reset it
    if (isAnimating) {
      console.log('Resetting stuck animation state on touch start');
      setIsAnimating(false);
    }

    handleTouchStart.current = {
      x: e.nativeEvent.pageX,
      y: e.nativeEvent.pageY
    };
    setIsSwiping(true);
  };

  const onTouchMove = (e: any) => {
    if (!isSwiping) return;

    const deltaX = e.nativeEvent.pageX - handleTouchStart.current.x;

    // If moved more than a threshold, it's not a tap anymore
    if (Math.abs(deltaX) > 10) {
      isTap.current = false;
    }
    const deltaY = e.nativeEvent.pageY - handleTouchStart.current.y;

    // Debug log for animation blocking
    if (isAnimating && Math.abs(deltaX) > 20) {
      console.log('Touch move blocked by isAnimating:', isAnimating, 'deltaX:', deltaX);
    }

    // Use requestAnimationFrame to throttle state updates and reduce re-renders
    if (!isAnimating) {
      requestAnimationFrame(() => {
        // Update both swipePosition state and swipeAnim for smooth animations
        const clampedDeltaY = Math.min(Math.max(deltaY, -100), 100);
        
        // Debug logging for animation values
        console.log('Animation Debug - deltaX:', deltaX, 'deltaY:', clampedDeltaY, 'swipeOverlay:', deltaX > 80 ? 'like' : deltaX < -80 ? 'pass' : 'none');
        
        setSwipePosition({
          x: deltaX,
          y: clampedDeltaY
        });
        
        // Enhanced animation with rotation and scaling effects
        const rotation = deltaX * 0.08; // Smooth rotation based on swipe
        const rotationDegrees = Math.max(-25, Math.min(25, rotation));
        const distance = Math.sqrt(deltaX * deltaX + clampedDeltaY * clampedDeltaY);
        const scale = Math.max(0.92, 1 - distance * 0.0003);
        
        // Update the Animated.Value for real-time animation with enhanced effects
        swipeAnim.setValue({ x: deltaX, y: clampedDeltaY });
        console.log('SwipeAnim setValue called with:', { x: deltaX, y: clampedDeltaY });

        // Show appropriate overlay based on direction
        if (deltaX > 80) {
          setSwipeOverlay('like');
          setSwipeTintColor('green');
          console.log('Setting overlay to LIKE (green)');
        } else if (deltaX < -80) {
          setSwipeOverlay('pass');
          setSwipeTintColor('red');
          console.log('Setting overlay to PASS (red)');
        } else {
          setSwipeOverlay(null);
          setSwipeTintColor('none');
        }
      });
    }
  };

  const onTouchEnd = (e: any) => {
    if (!isSwiping) return;

    const deltaX = e.nativeEvent.pageX - handleTouchStart.current.x;
    const deltaY = e.nativeEvent.pageY - handleTouchStart.current.y;

    // Debug log to check animation state
    console.log('Touch end - isAnimating:', isAnimating, 'deltaX:', deltaX);

    // Safety check: if isAnimating has been true for too long, reset it
    if (isAnimating) {
      console.log('Animation state is blocking swipe - this might indicate a stuck animation state');
    }

    // Check if we're showing example jobs when filteredJobs is empty
    const isShowingExampleJobs = filteredJobs.length === 0 && jobs.length === 0;
    
    // Detect if this was a tap (minimal movement in any direction)
    if (isTap.current && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
      // It was a tap, so open job details
      console.log('Card tapped, opening job details');
      if (safeCurrentJob) {
        openJobDetails(safeCurrentJob);
      } else if (isShowingExampleJobs) {
        // For example jobs, we can't open details, so just log
        console.log('Example job tapped - details not available');
      }
    }
    // Detect significant horizontal swipe - only trigger when user releases finger
    else if (Math.abs(deltaX) > 100 && !isAnimating) {
      const currentTime = Date.now();
      
      // Debounce swipes to prevent multiple rapid swipes
      if (currentTime - lastSwipeTime.current < SWIPE_DEBOUNCE_MS) {
        console.log('Swipe debounced - too soon after last swipe');
        setIsSwiping(false);
        return;
      }
      
      lastSwipeTime.current = currentTime;
      
      if (deltaX > 0) {
        // Right swipe - only execute when gesture is complete
        console.log('Swipe right gesture completed, recording swipe');
        setHasSwipedRight(true);
        
        if (isShowingExampleJobs) {
          console.log('Handling example job swipe right');
          // For example jobs, animate the swipe
          moveCardOffScreen('right');
          setTimeout(() => {
            setCurrentIndex(prev => prev + 1);
          }, 300);
        } else {
          handleSwipeRight();
        }
      } else {
        // Left swipe - only execute when gesture is complete
        console.log('Swipe left gesture completed, recording swipe');
        setHasSwipedLeft(true);
        
        if (isShowingExampleJobs) {
          console.log('Handling example job swipe left');
          // For example jobs, animate the swipe
          moveCardOffScreen('left');
          setTimeout(() => {
            setCurrentIndex(prev => prev + 1);
          }, 300);
        } else {
          handleSwipeLeft();
        }
      }
    } else {
      // Reset position if not swiped far enough - animate back to center
      Animated.spring(swipeAnim, {
        toValue: { x: 0, y: 0 },
        friction: 8,
        tension: 40,
        useNativeDriver: true
      }).start();
      
      setSwipePosition({ x: 0, y: 0 });
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    }

    // Always reset swiping state when touch ends
    setIsSwiping(false);
    
    // Only clear overlay and tint if no swipe was triggered
    if (Math.abs(deltaX) <= 100) {
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    }
  };

  // Animation refs for smoother transitions
  const nextCardAnim = useRef(new Animated.Value(0)).current;

  // Simple function to move to next card
  const goToNextCard = () => {
    // Create spring animation for the next card to become the current one
    Animated.spring(nextCardAnim, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true
    }).start(() => {
      // Reset animation value for next use
      nextCardAnim.setValue(0);

      // Check if we need to load more jobs (but don't call if already loading)
      // Fetch more jobs when we have 10 or fewer jobs left to ensure smooth experience
      if (currentIndex >= filteredJobs.length - 10 && !isLoadingJobsRef.current) {
        console.log('Running low on jobs (10 left), fetching more...');
        loadJobs();
      }

      // Update index and reset states
      setCurrentIndex(currentIndex + 1);
      setHasSwipedLeft(false);
      setHasSwipedRight(false);
      setSwipePosition({ x: 0, y: 0 });
    });
  };

  // Immediately handle a right swipe
  const handleSwipeRight = useCallback(async () => {
    if (!safeCurrentJob || isAnimating) {
      console.log('Swipe right blocked - no job or already animating');
      return;
    }

    // Set animating state immediately to prevent concurrent swipes
    setIsAnimating(true);
    console.log('Starting swipe right animation and recording');

    // Close modal if open to ensure smooth transition
    if (isJobDetailsVisible) {
      closeJobDetailsWithAnimation();
    }

    // Log the job being swiped right
    console.log('Swiping job right to save:', safeCurrentJob.id, safeCurrentJob.title);

    // Store job reference to avoid race conditions
    const jobToSwipe = { ...safeCurrentJob };
    const currentIndexSnapshot = currentIndex;

    // Add to applications in context
    addApplication(jobToSwipe);

    // Add to swiped jobs array to enable undo functionality
    setSwipedJobs(prev => {
      const newSwipedJobs = [...prev, { job: jobToSwipe, direction: 'right' as 'right', index: currentIndexSnapshot }];

      // Save swiped job IDs to AsyncStorage for persistence
      loadSwipedJobIds().then(existingIds => {
        const updatedIds = [...existingIds, jobToSwipe.id];
        saveSwipedJobIds(updatedIds);
      }).catch(err => console.error('Error updating swiped job IDs:', err));

      // Also mark as swiped in the enhanced queue system
      if (user?.id) {
        markJobSwiped(user.id, jobToSwipe.id).catch(err =>
          console.error('Error marking job swiped in backend:', err)
        );
      }

      return newSwipedJobs;
    });

    // Permanently remove this job from the filtered jobs array to prevent it from reappearing
    setFilteredJobs(prev => prev.filter(job => job.id !== jobToSwipe.id));
    console.log('Removed job from filtered jobs to prevent reappearing');

    // Record the enhanced swipe with job details for learning
    if (user) {
      // Get the match score for this job if available
      const recommendation = recommendations.find(rec => rec.job.id === jobToSwipe.id);
      const matchScore = recommendation?.overall_score;

      recordEnhancedSwipe(user.id, jobToSwipe, 'right', matchScore)
        .then(() => {
          console.log('Enhanced swipe recorded for learning algorithm');
          // Also record the interaction for analytics
          return recordRecommendationInteraction(user.id, jobToSwipe.id, {
            was_viewed: true,
            was_swiped: true,
            swipe_direction: 'right'
          });
        })
        .catch((err: Error) => {
          console.error('Failed to record enhanced swipe:', err);
          // Fallback to basic swipe recording
          return recordSwipe(jobToSwipe.id, user.id, 'like');
        })
        .catch((fallbackErr: Error) => {
          console.error('Failed to record fallback swipe:', fallbackErr);
        });

      // Save to bookmarks
      saveJobToBookmarks(jobToSwipe.id, user.id)
        .catch((err: Error) => {
          console.error('Failed to save to bookmarks:', err);
        });
    }

    // Set swiped state to trigger visual effect
    setHasSwipedRight(true);

    // Move to next card after a delay
    setTimeout(() => {
      goToNextCard();

      // Reset animation state
      setIsAnimating(false);
      setSwipeOverlay(null);
      setSwipeTintColor('none');
    }, 300);
  }, [safeCurrentJob, user, addApplication, currentIndex, isAnimating]);

  // Button handlers
  const handleButtonSwipeLeft = useCallback(() => {
    console.log('[DEBUG] Pass button pressed - safeCurrentJob:', !!safeCurrentJob, 'isAnimating:', isAnimating);
    
    // Check if we're showing example jobs when filteredJobs is empty
    const isShowingExampleJobs = filteredJobs.length === 0 && jobs.length === 0;
    
    if (!safeCurrentJob && !isShowingExampleJobs) {
      console.log('[DEBUG] No current job available for pass action');
      return;
    }
    if (isAnimating) {
      console.log('[DEBUG] Animation in progress, blocking pass action');
      return;
    }
    
    if (isShowingExampleJobs) {
      console.log('[DEBUG] Handling example job swipe left');
      // For example jobs, just move to the next card
      setCurrentIndex(prev => prev + 1);
      return;
    }
    
    console.log('[DEBUG] Calling handleSwipeLeft');
    handleSwipeLeft();
  }, [handleSwipeLeft, safeCurrentJob, isAnimating, filteredJobs.length, jobs.length]);

  const handleButtonSwipeRight = useCallback(() => {
    console.log('[DEBUG] Apply button pressed - safeCurrentJob:', !!safeCurrentJob, 'isAnimating:', isAnimating);
    
    // Check if we're showing example jobs when filteredJobs is empty
    const isShowingExampleJobs = filteredJobs.length === 0 && jobs.length === 0;
    
    if (!safeCurrentJob && !isShowingExampleJobs) {
      console.log('[DEBUG] No current job available for apply action');
      return;
    }
    if (isAnimating) {
      console.log('[DEBUG] Animation in progress, blocking apply action');
      return;
    }
    
    if (isShowingExampleJobs) {
      console.log('[DEBUG] Handling example job swipe right');
      // For example jobs, just move to the next card
      setCurrentIndex(prev => prev + 1);
      return;
    }
    
    console.log('[DEBUG] Calling handleSwipeRight');
    handleSwipeRight();
  }, [handleSwipeRight, safeCurrentJob, isAnimating, filteredJobs.length, jobs.length]);

  // Render the cards with touch handlers
  const renderCards = () => {
    if (isLoading) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={themeColors.tint} />
          <Text style={{ marginTop: 20, color: themeColors.text }}>Loading jobs...</Text>
        </View>
      );
    }

    if (filteredJobs.length === 0) {
      // If both jobs and filteredJobs are empty, show example jobs for testing
      if (jobs.length === 0) {
        const exampleJobs: Job[] = [
          {
            id: 'example-job-1',
            title: 'Senior Software Engineer',
            company: 'TechCorp',
            location: 'San Francisco, CA',
            pay: '$120,000 - $180,000',
            description: 'Join our innovative team as a Senior Software Engineer! We are looking for a passionate developer with 5+ years of experience in full-stack development. You will work on cutting-edge projects using React, Node.js, and cloud technologies. We offer competitive compensation, excellent benefits, and a collaborative work environment.',
            distance: '2.5 miles',
            tags: ['React', 'Node.js', 'TypeScript', 'AWS'],
            qualifications: ['Bachelor\'s degree in Computer Science', '5+ years of software development experience', 'Strong problem-solving skills'],
            requirements: ['Proficiency in React and Node.js', 'Experience with TypeScript', 'Knowledge of AWS services', 'Strong communication skills'],
            logo: 'https://via.placeholder.com/100x100/4285f4/ffffff?text=TC',
            image: 'https://via.placeholder.com/400x200/f0f0f0/333333?text=TechCorp+Office',
            // apiSource: 'example', // Removed: apiSource is not part of Job interface
            // cached: false, // Removed: cached is not part of Job interface
            aiEvaluation: {
              score: 85,
              fit_level: 'excellent',
              reasoning: 'Great match for your skills and experience level'
            }
          },
          {
            id: 'example-job-2',
            title: 'UX/UI Designer',
            company: 'DesignStudio',
            location: 'New York, NY',
            pay: '$85,000 - $120,000',
            description: 'We are seeking a creative UX/UI Designer to join our award-winning design team. You will be responsible for creating intuitive user experiences and beautiful interfaces for web and mobile applications. Experience with Figma, Adobe Creative Suite, and user research is essential.',
            distance: '1.8 miles',
            tags: ['Figma', 'Adobe XD', 'User Research', 'Prototyping'],
            qualifications: ['Bachelor\'s degree in Design or related field', '3+ years of UX/UI design experience', 'Portfolio demonstrating design skills'],
            requirements: ['Proficiency in Figma and Adobe Creative Suite', 'Experience with user research methods', 'Strong visual design skills', 'Ability to create interactive prototypes'],
            logo: 'https://via.placeholder.com/100x100/ff6b6b/ffffff?text=DS',
            image: 'https://via.placeholder.com/400x200/ff6b6b/ffffff?text=Design+Studio',
            // apiSource: 'example', // Removed: apiSource is not part of Job interface
            // cached: false, // Removed: cached is not part of Job interface
            aiEvaluation: {
              score: 78,
              fit_level: 'good',
              reasoning: 'Good creative match with room for growth'
            }
          },
          {
            id: 'example-job-3',
            title: 'Data Scientist',
            company: 'DataCorp Analytics',
            location: 'Austin, TX',
            pay: '$95,000 - $140,000',
            description: 'Join our data science team to unlock insights from complex datasets. You will work with machine learning algorithms, statistical analysis, and big data technologies. Strong background in Python, R, and SQL required. Experience with TensorFlow and PyTorch is a plus.',
            distance: '3.2 miles',
            tags: ['Python', 'Machine Learning', 'SQL', 'TensorFlow'],
            qualifications: ['Master\'s degree in Data Science, Statistics, or related field', '4+ years of data science experience', 'Strong analytical and mathematical skills'],
            requirements: ['Proficiency in Python, R, and SQL', 'Experience with machine learning frameworks', 'Knowledge of statistical analysis', 'Experience with big data technologies'],
            logo: 'https://via.placeholder.com/100x100/4ecdc4/ffffff?text=DC',
            image: 'https://via.placeholder.com/400x200/4ecdc4/ffffff?text=Data+Analytics',
            // apiSource: 'example', // Removed: apiSource is not part of Job interface
            // cached: false, // Removed: cached is not part of Job interface
            aiEvaluation: {
              score: 92,
              fit_level: 'excellent',
              reasoning: 'Perfect match for your analytical skills'
            }
          },
          {
            id: 'example-job-4',
            title: 'Marketing Manager',
            company: 'BrandBoost',
            location: 'Los Angeles, CA',
            pay: '$70,000 - $95,000',
            description: 'Lead our marketing initiatives and drive brand growth. You will develop marketing strategies, manage campaigns across multiple channels, and analyze performance metrics. Experience with digital marketing, social media, and content creation is required.',
            distance: '4.1 miles',
            tags: ['Digital Marketing', 'Social Media', 'Analytics', 'Content'],
            qualifications: ['Bachelor\'s degree in Marketing or related field', '5+ years of marketing experience', 'Leadership and team management skills'],
            requirements: ['Experience with digital marketing platforms', 'Social media management expertise', 'Analytics and data interpretation skills', 'Content creation and strategy development'],
            logo: 'https://via.placeholder.com/100x100/45b7d1/ffffff?text=BB',
            image: 'https://via.placeholder.com/400x200/45b7d1/ffffff?text=Marketing+Team',
            // apiSource: 'example', // Removed: apiSource is not part of Job interface
            // cached: false, // Removed: cached is not part of Job interface
            aiEvaluation: {
              score: 73,
              fit_level: 'fair',
              reasoning: 'Decent match with potential for development'
            }
          },
          {
            id: 'example-job-5',
            title: 'DevOps Engineer',
            company: 'CloudTech Solutions',
            location: 'Seattle, WA',
            pay: '$110,000 - $160,000',
            description: 'Build and maintain our cloud infrastructure and deployment pipelines. You will work with Kubernetes, Docker, AWS, and CI/CD tools to ensure reliable and scalable systems. Strong experience with automation and monitoring tools is essential.',
            distance: '2.9 miles',
            tags: ['Kubernetes', 'Docker', 'AWS', 'CI/CD'],
            qualifications: ['Bachelor\'s degree in Computer Science or related field', '4+ years of DevOps experience', 'Strong understanding of cloud architecture'],
            requirements: ['Proficiency in Kubernetes and Docker', 'Experience with AWS services', 'Knowledge of CI/CD pipelines', 'Automation and monitoring tools expertise'],
            logo: 'https://via.placeholder.com/100x100/96ceb4/ffffff?text=CT',
            image: 'https://via.placeholder.com/400x200/96ceb4/ffffff?text=Cloud+Infrastructure',
            // apiSource: 'example', // Removed: apiSource is not part of Job interface
            // cached: false, // Removed: cached is not part of Job interface
            aiEvaluation: {
              score: 88,
              fit_level: 'excellent',
              reasoning: 'Strong technical alignment with your background'
            }
          }
        ];
        
        // Use currentIndex to cycle through example jobs
        const exampleJob = exampleJobs[currentIndex % exampleJobs.length];
        
        return (
          <>
            {hasValidLogo(exampleJob) ? (
              <Animated.View
                style={[
                  styles.card,
                  getCardAnimatedStyle(),
                  {
                    zIndex: 1000
                  }
                ]}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}
              >
                <LinearGradient
                  colors={themeColors.cardGradient}
                  style={{ flex: 1, borderRadius: 24 }}
                >
                  <View style={styles.cardImageHalfWrapper}>
                    <SmartLogo
                      company={exampleJob.company}
                      logoUrl={exampleJob.image || getCompanyLogoUrl(exampleJob.company)}
                      style={styles.cardImageHalf}
                      resizeMode="cover"
                    />
                    <View style={styles.logoOverlayHalf}>
                      <SmartLogo
                        company={exampleJob.company}
                        logoUrl={exampleJob.logo || getCompanyLogoUrl(exampleJob.company)}
                        style={styles.companyLogo}
                        resizeMode="contain"
                      />
                    </View>
                  </View>
                  <View style={[styles.cardDetailsHalf, { backgroundColor: themeColors.card }]}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 4 }}>
                      <Text style={[styles.cardTitleModern, { color: themeColors.text, flex: 1 }]} numberOfLines={1}>
                        {exampleJob.title}
                      </Text>
                      <Badge
                        label="example"
                        showClock={false}
                        style={{ marginLeft: 8 }}
                      />
                    </View>
                    <Text style={[styles.cardCompany, { color: themeColors.textSecondary }]}>
                      {exampleJob.company}
                    </Text>
                    <Text style={[styles.cardLocationModern, { color: themeColors.textSecondary }]}>
                      {exampleJob.location}
                    </Text>
                    <View style={styles.tagsContainerModern}>
                      {exampleJob.tags.slice(0, 3).map((tag, index) => (
                        <View
                          key={index}
                          style={[styles.tagModern, { backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)' }]}
                        >
                          <Text style={[styles.tagTextModern, { color: themeColors.text }]}>
                            {tag}
                          </Text>
                        </View>
                      ))}
                    </View>
                  <View style={styles.payContainerModern}>
                    <FontAwesome name="money" size={16} color={themeColors.tint} style={{ marginRight: 6 }} />
                    <Text style={[styles.payModern, { color: themeColors.text }]}>
                      {exampleJob.pay}
                    </Text>
                  </View>
                  {exampleJob.aiEvaluation && (
                    <View style={styles.aiScoreContainer}>
                      <MaterialIcons name="psychology" size={14} color={themeColors.tint} />
                      <Text style={[styles.aiScoreText, { color: themeColors.text }]}>
                        AI Score: {exampleJob.aiEvaluation.score}/100
                      </Text>
                      <Text style={styles.aiScoreBadge}>
                        {exampleJob.aiEvaluation.fit_level === 'excellent' ? '🌟' :
                         exampleJob.aiEvaluation.fit_level === 'good' ? '👍' :
                         exampleJob.aiEvaluation.fit_level === 'fair' ? '⚖️' : '⚠️'}
                      </Text>
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Animated.View>
            ) : (
              <JobCardWithoutLogo
                job={{...exampleJob, apiSource: 'example'}}
                style={[
                  styles.card,
                  getCardAnimatedStyle(),
                  {
                    zIndex: 1000
                  }
                ]}
                onTouchStart={onTouchStart}
                onTouchMove={onTouchMove}
                onTouchEnd={onTouchEnd}
              />
            )}
            <View style={[styles.noJobsContainer, { position: 'absolute', bottom: 20, left: 20, right: 20, backgroundColor: 'rgba(0,0,0,0.7)', borderRadius: 12, padding: 16 }]}>
              <Text style={[styles.noJobsText, { color: '#fff', fontSize: 14, textAlign: 'center' }]}>
                📋 Example jobs for testing ({(currentIndex % exampleJobs.length) + 1}/5) - Swipe to see more!
              </Text>
            </View>
          </>
        );
      }
      
      // If jobs exist but filteredJobs is empty (due to filtering)
      return (
        <View style={styles.noJobsContainer}>
          <Text style={[styles.noJobsText, { color: themeColors.text }]}>
            No jobs match your criteria.
          </Text>
          <TouchableOpacity
            style={[styles.clearButton, { backgroundColor: themeColors.tint }]}
            onPress={() => {
              setSearchQuery('');
              setFilteredJobs(jobs);
            }}
          >
            <Text style={[styles.clearButtonText, { color: '#fff' }]}>Clear Filters</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Only render the current card and next card for performance
    const currentJob = filteredJobs[getCardIndex(currentIndex, filteredJobs)];
    const nextJob = filteredJobs[getCardIndex(currentIndex + 1, filteredJobs)];

    return (
      <>
        {/* Next card (shown underneath the current card) */}
        {nextJob && (
          hasValidLogo(nextJob) ? (
            <Animated.View
              key={`next-card-${currentIndex+1}`}
              style={[
                styles.card,
                styles.nextCardStyle,
                // Animate next card position when current card is swiped
                {
                  transform: [
                    {
                      scale: hasSwipedLeft || hasSwipedRight ?
                        nextCardAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.95, 1]
                        }) : 0.95
                    },
                    {
                      translateY: hasSwipedLeft || hasSwipedRight ?
                        nextCardAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [10, 0]
                        }) : 10
                    }
                  ],
                  zIndex: 999 // Lower z-index to stay below buttons by default
                }
              ]}
            >
              <LinearGradient
                colors={themeColors.cardGradient}
                style={{ flex: 1, borderRadius: 24 }}
              >
                {/* Next card content */}
                <View style={styles.cardImageHalfWrapper}>
                  <SmartLogo
                    company={nextJob.company}
                    logoUrl={nextJob.image || getCompanyLogoUrl(nextJob.company)}
                    style={styles.cardImageHalf}
                    resizeMode="cover"
                  />

                  {/* Company Logo Overlay */}
                  <View style={styles.logoOverlayHalf}>
                    <SmartLogo
                      company={nextJob.company}
                      logoUrl={nextJob.logo || getCompanyLogoUrl(nextJob.company)}
                      style={styles.companyLogo}
                      resizeMode="contain"
                    />
                  </View>
                </View>
                <View style={[styles.cardDetailsHalf, { backgroundColor: themeColors.card }]}>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 4 }}>
                    <Text style={[styles.cardTitleModern, { color: themeColors.text, flex: 1 }]} numberOfLines={1}>
                      {nextJob.title}
                    </Text>
                    <Badge
                      label={(nextJob as any).apiSource || 'api'}
                      showClock={(nextJob as any).cached === true}
                      style={{ marginLeft: 8 }}
                    />
                  </View>
                  <Text style={[styles.cardCompany, { color: themeColors.textSecondary }]}>
                    {nextJob.company}
                  </Text>
                  <Text style={[styles.cardLocationModern, { color: themeColors.textSecondary }]}>
                    {nextJob.location}
                  </Text>
                  <View style={styles.tagsContainerModern}>
                    {nextJob.tags.slice(0, 3).map((tag, index) => (
                      <View
                        key={index}
                        style={[styles.tagModern, { backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)' }]}
                      >
                        <Text style={[styles.tagTextModern, { color: themeColors.text }]}>
                          {tag}
                        </Text>
                      </View>
                    ))}
                  </View>
                  <View style={styles.payContainerModern}>
                    <FontAwesome name="money" size={16} color={themeColors.tint} style={{ marginRight: 6 }} />
                    <Text style={[styles.payModern, { color: themeColors.text }]}>
                      {nextJob.pay}
                    </Text>
                  </View>

                  {/* Compact AI Score Display for Next Job */}
                  {nextJob.aiEvaluation && (
                    <View style={styles.aiScoreContainer}>
                      <MaterialIcons name="psychology" size={14} color={themeColors.tint} />
                      <Text style={[styles.aiScoreText, { color: themeColors.text }]}>
                        AI Score: {nextJob.aiEvaluation.score}/100
                      </Text>
                      <Text style={styles.aiScoreBadge}>
                        {nextJob.aiEvaluation.fit_level === 'excellent' ? '🌟' :
                         nextJob.aiEvaluation.fit_level === 'good' ? '👍' :
                         nextJob.aiEvaluation.fit_level === 'fair' ? '⚖️' : '⚠️'}
                      </Text>
                    </View>
                  )}
                </View>
              </LinearGradient>
            </Animated.View>
          ) : (
            <JobCardWithoutLogo
              job={nextJob}
              style={[
                styles.card,
                styles.nextCardStyle,
                {
                  transform: [
                    {
                      scale: hasSwipedLeft || hasSwipedRight ?
                        nextCardAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.95, 1]
                        }) : 0.95
                    },
                    {
                      translateY: hasSwipedLeft || hasSwipedRight ?
                        nextCardAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [10, 0]
                        }) : 10
                    }
                  ],
                  zIndex: 999
                }
              ]}
            />
          )
        )}

        {/* Current card (on top) */}
        {currentJob && (
          hasValidLogo(currentJob) ? (
            <Animated.View
              key={`card-${currentIndex}`}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
              style={[
                styles.card,
                // Use the animated style function for consistent animations
                getCardAnimatedStyle(),
                {
                  zIndex: Math.abs(swipePosition.x) > 10 ? 3000 : 1000 // Higher z-index when actively swiping, lower when static
                },
                // Border styling based on swipe direction
                hasSwipedLeft && { borderColor: '#FF3B30', borderWidth: 2 },
                hasSwipedRight && { borderColor: '#4CD964', borderWidth: 2 },
                // Dynamic border during swipe
                swipeOverlay === 'like' && { borderColor: '#4CD964', borderWidth: 2 },
                swipeOverlay === 'pass' && { borderColor: '#FF3B30', borderWidth: 2 },
              ]}
            >
            <LinearGradient
              colors={themeColors.cardGradient}
              style={{ flex: 1, borderRadius: 24 }}
            >
              {/* Color tint overlay for right swipe (green) */}
              <Animated.View
                style={[
                  StyleSheet.absoluteFillObject,
                  {
                    backgroundColor: swipeAnim.x.interpolate({
                      inputRange: [0, 50, 100, 150],
                      outputRange: ['rgba(76, 217, 100, 0)', 'rgba(76, 217, 100, 0.3)', 'rgba(52, 199, 89, 0.5)', 'rgba(40, 167, 69, 0.7)'],
                      extrapolate: 'clamp'
                    }),
                    borderRadius: 24,
                    zIndex: 100,
                    opacity: swipeAnim.x.interpolate({
                      inputRange: [0, 30],
                      outputRange: [0, 1],
                      extrapolate: 'clamp'
                    })
                  }
                ]}
                pointerEvents="none"
              />

              {/* Color tint overlay for left swipe (red) */}
              <Animated.View
                style={[
                  StyleSheet.absoluteFillObject,
                  {
                    backgroundColor: swipeAnim.x.interpolate({
                      inputRange: [-150, -100, -50, 0],
                      outputRange: ['rgba(220, 53, 69, 0.8)', 'rgba(255, 59, 48, 0.6)', 'rgba(255, 99, 71, 0.4)', 'rgba(255, 59, 48, 0)'],
                      extrapolate: 'clamp'
                    }),
                    borderRadius: 24,
                    zIndex: 100,
                    opacity: swipeAnim.x.interpolate({
                      inputRange: [-30, 0],
                      outputRange: [1, 0],
                      extrapolate: 'clamp'
                    })
                  }
                ]}
                pointerEvents="none"
              />
              {/* Card Content */}
              <View style={styles.cardImageHalfWrapper}>
                  <SmartLogo
                    company={currentJob.company}
                    logoUrl={currentJob.image || getCompanyLogoUrl(currentJob.company)}
                    jobTitle={currentJob.title}
                    style={styles.cardImageHalf}
                    resizeMode="cover"
                  />

                  {/* Company Logo Overlay */}
                  <View style={styles.logoOverlayHalf}>
                    <SmartLogo
                      company={currentJob.company}
                      logoUrl={currentJob.logo || getCompanyLogoUrl(currentJob.company)}
                      jobTitle={currentJob.title}
                      style={styles.companyLogo}
                      resizeMode="contain"
                    />
                  </View>
              </View>

              <View style={[styles.cardDetailsHalf, { backgroundColor: themeColors.card }]}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 4 }}>
                  <Text style={[styles.cardTitleModern, { color: themeColors.text, flex: 1 }]} numberOfLines={1}>
                    {currentJob.title}
                  </Text>
                  <Badge
                    label={(currentJob as any).apiSource || 'api'}
                    showClock={(currentJob as any).cached === true}
                    style={{ marginLeft: 8 }}
                  />
                </View>
                <Text style={[styles.cardCompany, { color: themeColors.textSecondary }]}>
                  {currentJob.company}
                </Text>
                <Text style={[styles.cardLocationModern, { color: themeColors.textSecondary }]}>
                  {currentJob.location}
                </Text>
                <View style={styles.tagsContainerModern}>
                  {currentJob.tags.slice(0, 3).map((tag, index) => (
                    <View
                      key={index}
                      style={[styles.tagModern, { backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)' }]}
                    >
                      <Text style={[styles.tagTextModern, { color: themeColors.text }]}>
                        {tag}
                      </Text>
                    </View>
                  ))}
                </View>
                <View style={styles.payContainerModern}>
                  <FontAwesome name="money" size={16} color={themeColors.tint} style={{ marginRight: 6 }} />
                  <Text style={[styles.payModern, { color: themeColors.text }]}>
                    {currentJob.pay}
                  </Text>
                </View>
                <View style={styles.distanceModern}>
                  <FontAwesome name="map-marker" size={16} color={themeColors.textSecondary} />
                  <Text style={[styles.distanceTextModern, { color: themeColors.textSecondary }]}>
                    {currentJob.distance}
                  </Text>
                </View>
                
                {/* Compact AI Score Display */}
                {currentJob.aiEvaluation && (
                  <View style={styles.aiScoreContainer}>
                    <MaterialIcons name="psychology" size={14} color={themeColors.tint} />
                    <Text style={[styles.aiScoreText, { color: themeColors.text }]}>
                      AI Score: {currentJob.aiEvaluation.score}/100
                    </Text>
                    <Text style={styles.aiScoreBadge}>
                      {currentJob.aiEvaluation.fit_level === 'excellent' ? '🌟' : 
                       currentJob.aiEvaluation.fit_level === 'good' ? '👍' :
                       currentJob.aiEvaluation.fit_level === 'fair' ? '⚖️' : '⚠️'}
                    </Text>
                  </View>
                )}
              </View>
            </LinearGradient>

            {/* Dynamic PASS label */}
            {swipePosition.x < -20 && (
              <View
                style={[
                  styles.passLabelTopContainer,
                  {
                    transform: [
                      { rotate: '25deg' },
                      { scale: 1 + Math.min(Math.abs(swipePosition.x) / 200, 0.8) }
                    ],
                    opacity: Math.min(Math.abs(swipePosition.x) / 100, 1)
                  }
                ]}
              >
                <Text
                  style={[
                    styles.actionLabelTop,
                    {
                      fontSize: 24 + Math.min(Math.abs(swipePosition.x) / 10, 18),
                      letterSpacing: 1.5
                    }
                  ]}
                >
                  PASS
                </Text>
              </View>
            )}

            {/* Dynamic APPLY label */}
            {swipePosition.x > 20 && (
              <View
                style={[
                  styles.applyLabelTopContainer,
                  {
                    transform: [
                      { rotate: '-25deg' },
                      { scale: 1 + Math.min(Math.abs(swipePosition.x) / 200, 0.8) }
                    ],
                    opacity: Math.min(Math.abs(swipePosition.x) / 100, 1)
                  }
                ]}
              >
                <Text
                  style={[
                    styles.actionLabelTop,
                    {
                      fontSize: 24 + Math.min(Math.abs(swipePosition.x) / 10, 18),
                      letterSpacing: 1.5
                    }
                  ]}
                >
                  APPLY
                </Text>
              </View>
            )}

            {/* Show overlays based on swipe state or animation state */}
            {(hasSwipedRight || swipeOverlay === 'like') && (
              <View style={[styles.overlayBadge, styles.likeBadge]}>
                <FontAwesome name="check" size={28} color="#fff" />
                <Text style={styles.overlayText}>SAVE</Text>
              </View>
            )}

            {(hasSwipedLeft || swipeOverlay === 'pass') && (
              <View style={[styles.overlayBadge, styles.passBadge]}>
                <FontAwesome name="close" size={28} color="#fff" />
                <Text style={styles.overlayText}>PASS</Text>
              </View>
            )}
          </Animated.View>
          ) : (
            <JobCardWithoutLogo
              job={currentJob}
              style={[
                styles.card,
                getCardAnimatedStyle(),
                {
                  zIndex: Math.abs(swipePosition.x) > 10 ? 3000 : 1000
                },
                hasSwipedLeft && { borderColor: '#FF3B30', borderWidth: 2 },
                hasSwipedRight && { borderColor: '#4CD964', borderWidth: 2 },
                swipeOverlay === 'like' && { borderColor: '#4CD964', borderWidth: 2 },
                swipeOverlay === 'pass' && { borderColor: '#FF3B30', borderWidth: 2 },
              ]}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
              showSwipeOverlays={true}
            />
          )
        )}
      </>
    );
  };

  // Render the inbox view
  // Removed renderInbox function - simplified to browse only

  // Render the search modal
  const renderSearchModal = () => {
    return (
      <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' }}>
        <View style={[styles.searchModalContainer, { backgroundColor: themeColors.card, width: '90%' }]}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: themeColors.text, marginBottom: 16 }}>
            Search Jobs
          </Text>
          <TextInput
            style={{ height: 50, borderColor: themeColors.border, borderWidth: 1, paddingHorizontal: 16, marginBottom: 16, borderRadius: 8, color: themeColors.text }}
            placeholder="Job title, company, or keywords"
            placeholderTextColor={themeColors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <TouchableOpacity
            style={{ backgroundColor: themeColors.tint, paddingVertical: 12, borderRadius: 8, alignItems: 'center' }}
            onPress={() => {
              const filtered = jobs.filter(job =>
                job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
                job.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
              );
              setFilteredJobs(filtered);
              setCurrentIndex(0);
              setIsSearchVisible(false);
            }}
          >
            <Text style={{ color: '#fff', fontSize: 16, fontWeight: '600' }}>Search</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ marginTop: 16, alignItems: 'center' }}
            onPress={() => setIsSearchVisible(false)}
          >
            <Text style={{ color: themeColors.textSecondary, fontSize: 16 }}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Helper function to generate company logo URL with fallback
  const getCompanyLogoUrl = (company: string, size: number = 300) => {
    const cleanCompanyName = company.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
    return `https://logo.clearbit.com/${cleanCompanyName}.com?size=${size}`;
  };

  // Helper function to determine if a job has a valid logo
  const hasValidLogo = (job: any) => {
    const logoUrl = job.logo || job.image || getCompanyLogoUrl(job.company);

    // Check if logo is likely to fail or is invalid
    const isKnownFailure = failedLogosCache.has(logoUrl);
    const isClearbitUrl = logoUrl && logoUrl.includes('logo.clearbit.com');
    const hasNoLogo = !logoUrl || logoUrl.trim() === '' || logoUrl.includes('placeholder') || logoUrl.includes('no-logo');

    // Return true only if we have a valid, non-clearbit logo that hasn't failed before
    return !isKnownFailure && !isClearbitUrl && !hasNoLogo && job.logo && job.logo.trim() !== '';
  };

  // Component for rendering job cards without logos (title-focused layout)
  const JobCardWithoutLogo: React.FC<{
    job: any;
    style: any;
    onTouchStart?: (event: any) => void;
    onTouchMove?: (event: any) => void;
    onTouchEnd?: (event: any) => void;
    showSwipeOverlays?: boolean;
  }> = ({ job, style, onTouchStart, onTouchMove, onTouchEnd, showSwipeOverlays = false }) => {
    return (
      <Animated.View
        style={style}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        <LinearGradient
          colors={themeColors.cardGradient}
          style={{ flex: 1, borderRadius: 24 }}
        >
          {/* Color tint overlays for swipe feedback (only for current card) */}
          {showSwipeOverlays && (
            <>
              {/* Color tint overlay for right swipe (green) */}
              <Animated.View
                style={[
                  StyleSheet.absoluteFillObject,
                  {
                    backgroundColor: swipeAnim.x.interpolate({
                      inputRange: [0, 50, 100, 150],
                      outputRange: ['rgba(76, 217, 100, 0)', 'rgba(76, 217, 100, 0.3)', 'rgba(52, 199, 89, 0.5)', 'rgba(40, 167, 69, 0.7)'],
                      extrapolate: 'clamp'
                    }),
                    borderRadius: 24,
                    zIndex: 100,
                    opacity: swipeAnim.x.interpolate({
                      inputRange: [0, 30],
                      outputRange: [0, 1],
                      extrapolate: 'clamp'
                    })
                  }
                ]}
                pointerEvents="none"
              />

              {/* Color tint overlay for left swipe (red) */}
              <Animated.View
                style={[
                  StyleSheet.absoluteFillObject,
                  {
                    backgroundColor: swipeAnim.x.interpolate({
                      inputRange: [-150, -100, -50, 0],
                      outputRange: ['rgba(220, 53, 69, 0.8)', 'rgba(255, 59, 48, 0.6)', 'rgba(255, 99, 71, 0.4)', 'rgba(255, 59, 48, 0)'],
                      extrapolate: 'clamp'
                    }),
                    borderRadius: 24,
                    zIndex: 100,
                    opacity: swipeAnim.x.interpolate({
                      inputRange: [-30, 0],
                      outputRange: [1, 0],
                      extrapolate: 'clamp'
                    })
                  }
                ]}
                pointerEvents="none"
              />
            </>
          )}

          {/* Full height content area without image section */}
          <View style={[styles.cardDetailsFullHeight, { backgroundColor: themeColors.card }]}>
            {/* Large job title at the top */}
            <View style={styles.titleSection}>
              <Text style={[styles.cardTitleLarge, { color: themeColors.text }]} numberOfLines={3}>
                {job.title}
              </Text>
            </View>

            {/* Job details section */}
            <View style={styles.jobDetailsSection}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
                <Text style={[styles.cardCompany, { color: themeColors.textSecondary, flex: 1 }]} numberOfLines={1}>
                  {job.company}
                </Text>
                <Badge
                  label={(job as any).apiSource || 'api'}
                  showClock={(job as any).cached === true}
                  style={{ marginLeft: 8 }}
                />
              </View>
              <Text style={[styles.cardLocationModern, { color: themeColors.textSecondary }]}>
                {job.location}
              </Text>
              <View style={styles.tagsContainerModern}>
                {job.tags.slice(0, 3).map((tag: string, index: number) => (
                  <View
                    key={index}
                    style={[
                      styles.tagModern,
                      { backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)' }
                    ]}
                  >
                    <Text style={[styles.tagTextModern, { color: themeColors.text }]}>
                      {tag}
                    </Text>
                  </View>
                ))}
              </View>

              {/* Additional job details for current card */}
              {showSwipeOverlays && (
                <>
                  <View style={styles.payContainerModern}>
                    <FontAwesome name="money" size={16} color={themeColors.tint} style={{ marginRight: 6 }} />
                    <Text style={[styles.payModern, { color: themeColors.text }]}>
                      {job.pay}
                    </Text>
                  </View>
                  <View style={styles.distanceModern}>
                    <FontAwesome name="map-marker" size={16} color={themeColors.textSecondary} />
                    <Text style={[styles.distanceTextModern, { color: themeColors.textSecondary }]}>
                      {job.distance}
                    </Text>
                  </View>

                  {/* Compact AI Score Display */}
                  {job.aiEvaluation && (
                    <View style={styles.aiScoreContainer}>
                      <MaterialIcons name="psychology" size={14} color={themeColors.tint} />
                      <Text style={[styles.aiScoreText, { color: themeColors.text }]}>
                        AI Score: {job.aiEvaluation.score}/100
                      </Text>
                      <Text style={styles.aiScoreBadge}>
                        {job.aiEvaluation.fit_level === 'excellent' ? '🌟' :
                         job.aiEvaluation.fit_level === 'good' ? '👍' :
                         job.aiEvaluation.fit_level === 'fair' ? '⚖️' : '⚠️'}
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
          </View>
        </LinearGradient>

        {/* Swipe overlays and labels (only for current card) */}
        {showSwipeOverlays && (
          <>
            {/* Dynamic PASS label */}
            {swipePosition.x < -20 && (
              <View
                style={[
                  styles.passLabelTopContainer,
                  {
                    transform: [
                      { rotate: '25deg' },
                      { scale: 1 + Math.min(Math.abs(swipePosition.x) / 200, 0.8) }
                    ],
                    opacity: Math.min(Math.abs(swipePosition.x) / 100, 1)
                  }
                ]}
              >
                <Text
                  style={[
                    styles.actionLabelTop,
                    {
                      fontSize: 24 + Math.min(Math.abs(swipePosition.x) / 10, 18),
                      letterSpacing: 1.5
                    }
                  ]}
                >
                  PASS
                </Text>
              </View>
            )}

            {/* Dynamic APPLY label */}
            {swipePosition.x > 20 && (
              <View
                style={[
                  styles.applyLabelTopContainer,
                  {
                    transform: [
                      { rotate: '-25deg' },
                      { scale: 1 + Math.min(Math.abs(swipePosition.x) / 200, 0.8) }
                    ],
                    opacity: Math.min(Math.abs(swipePosition.x) / 100, 1)
                  }
                ]}
              >
                <Text
                  style={[
                    styles.actionLabelTop,
                    {
                      fontSize: 24 + Math.min(Math.abs(swipePosition.x) / 10, 18),
                      letterSpacing: 1.5
                    }
                  ]}
                >
                  APPLY
                </Text>
              </View>
            )}

            {/* Show overlays based on swipe state or animation state */}
            {(hasSwipedRight || swipeOverlay === 'like') && (
              <View style={[styles.overlayBadge, styles.likeBadge]}>
                <FontAwesome name="check" size={28} color="#fff" />
                <Text style={styles.overlayText}>SAVE</Text>
              </View>
            )}

            {(hasSwipedLeft || swipeOverlay === 'pass') && (
              <View style={[styles.overlayBadge, styles.passBadge]}>
                <FontAwesome name="close" size={28} color="#fff" />
                <Text style={styles.overlayText}>PASS</Text>
              </View>
            )}
          </>
        )}
      </Animated.View>
    );
  };

  // Enhanced job details modal with smooth slide-up animation and gesture handling
  const renderJobDetailsModal = () => {
    if (!selectedJob) return null;

    // Job details panel content
    const companyLogo = selectedJob.logo || getCompanyLogoUrl(selectedJob.company);

    return (
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeJobDetails}
      >
        <View style={[StyleSheet.absoluteFillObject, { backgroundColor: 'transparent' }]}>
          {/* Enhanced backdrop with dynamic opacity */}
          <Animated.View
            style={[
              StyleSheet.absoluteFillObject,
              {
                backgroundColor: 'rgba(0,0,0,1)',
                opacity: backdropOpacityAnim,
              }
            ]}
          >
            {/* Touchable backdrop to close when tapping outside */}
            <TouchableWithoutFeedback onPress={closeJobDetails}>
              <View style={StyleSheet.absoluteFillObject} />
            </TouchableWithoutFeedback>
          </Animated.View>

          {/* Enhanced sliding panel with gesture handling */}
          <Animated.View
            {...modalPanResponder.panHandlers}
            style={[
              styles.enhancedModalPanel,
              {
                backgroundColor: themeColors.card,
                transform: [
                  { translateY: Animated.add(modalSlideAnim, modalDragY) }
                ],
              }
            ]}
          >
            {/* Enhanced drag handle with visual feedback */}
            <View style={styles.enhancedDragHandle}>
              <View style={[styles.dragIndicator, { backgroundColor: themeColors.textSecondary }]} />
              {isDragging && (
                <Text style={[styles.dragHint, { color: themeColors.textSecondary }]}>
                  Release to close
                </Text>
              )}
            </View>

            {/* Enhanced content container with proper scrolling */}
            <View style={styles.enhancedPanelContent}>
              <ScrollView>
                {/* Header section with logo and close button */}
                <View style={styles.detailsHeader}>
                  {/* Company logo */}
                  <View style={styles.companyLogoContainer}>
                    <SmartLogo
                      company={selectedJob.company}
                      logoUrl={companyLogo}
                      jobTitle={selectedJob.title}
                      style={styles.companyLogoLarge}
                      resizeMode="contain"
                    />
                  </View>

                  {/* Close button */}
                  <TouchableOpacity
                    style={styles.detailsCloseButton}
                    onPress={closeJobDetails}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="close" size={20} color={themeColors.text} />
                  </TouchableOpacity>
                </View>

                {/* Job title and basic info */}
                <View style={styles.jobMainDetails}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                    <Text style={[styles.jobDetailsTitleText, { color: themeColors.text, flex: 1 }]}>
                      {selectedJob.title}
                    </Text>
                    {isViewingFromInbox && (
                      <View style={[styles.statusBadge, { backgroundColor: '#4CD964' + '20', marginLeft: 8 }]}>
                        <FontAwesome name="check-circle" size={14} color="#4CD964" style={{ marginRight: 4 }} />
                        <Text style={[styles.statusText, { color: '#4CD964', fontSize: 12 }]}>Applied</Text>
                      </View>
                    )}
                  </View>
                  <Text style={[styles.jobDetailsCompany, { color: themeColors.textSecondary }]}>
                    {selectedJob.company}
                  </Text>
                  <Text style={[styles.jobDetailsLocationText, { color: themeColors.textSecondary }]}>
                    {selectedJob.location}
                  </Text>
                  <Text style={[styles.jobDetailsPayText, { color: themeColors.tint }]}>
                    {selectedJob.pay}
                  </Text>

                  {/* Tags section */}
                  <View style={styles.tagsContainerModern}>
                    {selectedJob.tags.map((tag, index) => (
                      <View
                        key={index}
                        style={[styles.tagModern, { backgroundColor: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)' }]}
                      >
                        <Text style={[styles.tagTextModern, { color: themeColors.text }]}>
                          {tag}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>

                {/* Action buttons - Context-aware based on where opened from */}
                <View style={styles.jobActionButtons}>
                  {/* Always render all buttons but conditionally show them */}
                  <TouchableOpacity
                    style={[
                      styles.jobActionButton, 
                      {backgroundColor: '#FF3B30' + '15'},
                      isViewingFromInbox && {display: 'none'}
                    ]}
                    onPress={() => {
                      console.log('Pass button pressed');
                      closeJobDetails();
                      handleSwipeLeft();
                    }}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="close" size={24} color="#FF3B30" />
                    <Text style={[styles.jobActionText, {color: '#FF3B30'}]}>Pass</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.jobActionButton, 
                      {backgroundColor: themeColors.tint + '15'},
                      isViewingFromInbox && {display: 'none'}
                    ]}
                    onPress={() => {
                      console.log('Save button pressed');
                      addApplication(selectedJob);
                      closeJobDetails();
                      showNotification('Job saved to inbox');
                    }}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="bookmark" size={22} color={themeColors.tint} />
                    <Text style={[styles.jobActionText, {color: themeColors.tint}]}>Save</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.jobActionButton, 
                      {backgroundColor: themeColors.tint + '15'},
                      !isViewingFromInbox && {display: 'none'}
                    ]}
                    onPress={() => {
                      console.log('Edit Cover Letter button pressed');
                      closeJobDetails();
                      openCoverLetterModal(selectedJob);
                    }}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="edit" size={22} color={themeColors.tint} />
                    <Text style={[styles.jobActionText, {color: themeColors.tint}]}>Cover Letter</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.jobActionButton, 
                      {backgroundColor: '#4CD964' + '15'},
                      !isViewingFromInbox && {display: 'none'}
                    ]}
                    onPress={() => {
                      console.log('Application Status button pressed');
                      closeJobDetails();
                      showNotification('Application submitted successfully');
                    }}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="check-circle" size={22} color="#4CD964" />
                    <Text style={[styles.jobActionText, {color: '#4CD964'}]}>Applied</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.jobActionButton, 
                      {backgroundColor: '#4CD964' + '15'},
                      isViewingFromInbox && {display: 'none'}
                    ]}
                    onPress={() => {
                      console.log('Apply button pressed');
                      closeJobDetails();
                      handleSwipeRight();
                    }}
                    activeOpacity={0.7}
                  >
                    <FontAwesome name="check" size={24} color="#4CD964" />
                    <Text style={[styles.jobActionText, {color: '#4CD964'}]}>Apply</Text>
                  </TouchableOpacity>
                </View>

                {/* Enhanced AI Job Evaluation */}
                <EnhancedAIJobEvaluationComponent
                  job={selectedJob}
                  themeColors={themeColors}
                />

                {/* Description section */}
                <View style={styles.sectionContainer}>
                  <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                    About This Role
                  </Text>
                  {(() => {
                    // Try different description fields in order of preference
                    const description = (selectedJob as any).cleanDescription || 
                                      selectedJob.description || 
                                      (selectedJob as any).summary ||
                                      `Join ${selectedJob.company} as a ${selectedJob.title}. This role offers competitive compensation and growth opportunities.`;
                    
                    // If description contains HTML tags, use RenderHTML, otherwise use Text
                    if (description.includes('<') && description.includes('>')) {
                      return (
                        <RenderHTML
                          contentWidth={windowWidth}
                          source={{ html: description }}
                          tagsStyles={{
                            p: { color: themeColors.textSecondary, fontSize: 16, lineHeight: 24, marginBottom: 10 },
                            ul: { color: themeColors.textSecondary, fontSize: 16, lineHeight: 24, marginLeft: 20 },
                            li: { color: themeColors.textSecondary, fontSize: 16, lineHeight: 24, marginBottom: 8 },
                            a: { color: themeColors.tint, textDecorationLine: 'none' },
                            b: { fontWeight: 'bold', color: themeColors.text },
                            strong: { fontWeight: 'bold', color: themeColors.text },
                          }}
                        />
                      );
                    } else {
                      return (
                        <Text style={[styles.jobDetailsDescription, { color: themeColors.textSecondary }]}>
                          {description}
                        </Text>
                      );
                    }
                  })()}
                </View>

                {/* AI Explanation section */}
                {selectedJob.aiExplanation && (
                  <View style={styles.sectionContainer}>
                    <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                      AI Explained
                    </Text>
                    <View style={[styles.aiExplanationContainer, { backgroundColor: themeColors.tint + '15', borderColor: themeColors.tint + '30' }]}>
                      <FontAwesome name="lightbulb-o" size={20} color={themeColors.tint} style={styles.aiExplanationIcon} />
                      <Text style={[styles.sectionContent, { color: themeColors.textSecondary, flex: 1 }]}>
                        {selectedJob.aiExplanation}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Qualifications section */}
                <View style={styles.sectionContainer}>
                  <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                    Qualifications
                  </Text>
                  {selectedJob.qualifications && selectedJob.qualifications.length > 0 ? (
                    selectedJob.qualifications.map((qualification, index) => (
                      <View key={`qual-${index}`} style={styles.bulletPoint}>
                        <Text style={[styles.bullet, { color: themeColors.textSecondary }]}>•</Text>
                        <Text style={[styles.sectionContent, { color: themeColors.textSecondary, flex: 1 }]}>
                          {qualification.replace(/<[^>]*>?/gm, '')}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <Text style={[styles.sectionContent, { color: themeColors.textSecondary }]}>No qualifications listed.</Text>
                  )}
                </View>

                {/* Requirements section */}
                <View style={styles.sectionContainer}>
                  <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                    Requirements
                  </Text>
                  {selectedJob.requirements && selectedJob.requirements.length > 0 ? (
                    selectedJob.requirements.map((requirement, index) => (
                      <View key={`req-${index}`} style={styles.bulletPoint}>
                        <Text style={[styles.bullet, { color: themeColors.textSecondary }]}>•</Text>
                        <Text style={[styles.sectionContent, { color: themeColors.textSecondary, flex: 1 }]}>
                          {requirement.replace(/<[^>]*>?/gm, '')}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <Text style={[styles.sectionContent, { color: themeColors.textSecondary }]}>No requirements listed.</Text>
                  )}
                </View>

                {/* Add bottom spacing for better scroll experience */}
                <View style={{height: 100}} />
              </ScrollView>
            </View>
          </Animated.View>
        </View>
        {/* Action buttons overlay when modal is visible */}
        <View style={[
          styles.buttonsContainer,
          { bottom: insets.bottom ? insets.bottom + 6 : 16 }
        ]}>
          <TouchableOpacity
            style={[styles.button, styles.undoButton, { backgroundColor: themeColors.card }]}
            onPress={handleUndoSwipe}
            activeOpacity={0.7}
            disabled={swipedJobs.length === 0}
            onPressIn={() => setUndoActive(true)}
            onPressOut={() => setUndoActive(false)}
          >
            <Animated.View style={[styles.buttonInner, { borderColor: theme === 'dark' ? '#222' : '#FFC107', opacity: swipedJobs.length === 0 ? 0.4 : 1, backgroundColor: undoActive ? (theme === 'dark' ? '#FFC107' : '#FFF8E1') : 'transparent' }] }>
              <FontAwesome name="undo" size={24} color={undoActive ? (theme === 'dark' ? '#222' : '#FFC107') : '#fff'} />
            </Animated.View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.passButton, { backgroundColor: themeColors.card }]}
            onPress={handleButtonSwipeLeft}
            activeOpacity={0.7}
            onPressIn={() => { animateButton(passScale, 0.92); setPassActive(true); }}
            onPressOut={() => { animateButton(passScale, 1); setPassActive(false); }}
          >
            <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : redColor, transform: [{ scale: passScale }], backgroundColor: passActive ? (theme === 'dark' ? '#FF5252' : '#FFEBEE') : 'transparent' }] }>
              <FontAwesome name="close" size={26} color={passActive ? iconColorActive : iconColorInactive} />
            </Animated.View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.infoButton, { backgroundColor: themeColors.card }]}
            onPress={() => {
              console.log('Info button pressed, current job:', safeCurrentJob?.title);
              if (safeCurrentJob) {
                openJobDetails(safeCurrentJob);
              } else {
                console.warn('No current job available for info button');
              }
            }}
            activeOpacity={0.7}
            onPressIn={() => { animateButton(infoScale, 0.92); setInfoActive(true); }}
            onPressOut={() => { animateButton(infoScale, 1); setInfoActive(false); }}
          >
            <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : blueColor, transform: [{ scale: infoScale }], backgroundColor: infoActive ? (theme === 'dark' ? '#42A5F5' : '#E3F2FD') : 'transparent' }] }>
              <FontAwesome name="info-circle" size={26} color={infoActive ? iconColorActive : iconColorInactive} />
            </Animated.View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.likeButton, { backgroundColor: themeColors.card }]}
            onPress={handleButtonSwipeRight}
            activeOpacity={0.7}
            onPressIn={() => { animateButton(likeScale, 0.92); setLikeActive(true); }}
            onPressOut={() => { animateButton(likeScale, 1); setLikeActive(false); }}
          >
            <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : greenColor, transform: [{ scale: likeScale }], backgroundColor: likeActive ? (theme === 'dark' ? '#4CAF50' : '#E8F5E9') : 'transparent' }] }>
              <FontAwesome name="check-circle" size={26} color={likeActive ? iconColorActive : iconColorInactive} />
            </Animated.View>
          </TouchableOpacity>
        </View>
      </Modal>
    );
  };

  // Removed old filter UI - using card-based filters only

  // Get animated style for the current card
  const getCardAnimatedStyle = () => {
    const rotate = swipeAnim.x.interpolate({
      inputRange: [-SCREEN_WIDTH / 1.5, 0, SCREEN_WIDTH / 1.5],
      outputRange: ['-25deg', '0deg', '25deg'], // Increased rotation for more visible effect
      extrapolate: 'clamp',
    });

    const scale = swipeAnim.x.interpolate({
      inputRange: [-SCREEN_WIDTH / 2, 0, SCREEN_WIDTH / 2],
      outputRange: [0.85, 1, 0.85], // More pronounced scaling
      extrapolate: 'clamp',
    });

    const opacity = swipeAnim.x.interpolate({
      inputRange: [-SCREEN_WIDTH / 1.5, 0, SCREEN_WIDTH / 1.5],
      outputRange: [0.6, 1, 0.6], // More pronounced opacity change
      extrapolate: 'clamp',
    });

    // Add debug logging for web platform
    if (Platform.OS === 'web') {
      console.log('🎨 Animation values:', {
        x: swipeAnim.x._value,
        y: swipeAnim.y._value,
        swipePosition: swipePosition
      });
    }

    // Enhanced color fade based on swipe direction with more prominent effects
    const backgroundColor = swipeAnim.x.interpolate({
      inputRange: [-SCREEN_WIDTH / 2, -50, 0, 50, SCREEN_WIDTH / 2],
      outputRange: [
        theme === 'dark' ? 'rgba(255, 68, 68, 0.8)' : 'rgba(255, 68, 68, 0.6)', // Strong red fade for left swipe
        theme === 'dark' ? 'rgba(255, 68, 68, 0.3)' : 'rgba(255, 68, 68, 0.2)', // Light red fade
        themeColors.card, // Original color
        theme === 'dark' ? 'rgba(76, 217, 100, 0.3)' : 'rgba(76, 217, 100, 0.2)', // Light green fade
        theme === 'dark' ? 'rgba(76, 217, 100, 0.8)' : 'rgba(76, 217, 100, 0.6)' // Strong green fade for right swipe
      ],
      extrapolate: 'clamp',
    });

    return {
      transform: [
        { rotate },
        { scale },
        { translateX: swipeAnim.x },
        { translateY: swipeAnim.y }
      ],
      opacity,
      borderRadius: 22,
      shadowColor: '#000',
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 5,
      height: CARD_HEIGHT,
      backgroundColor,
    };
  };

  const moveCardOffScreen = (direction: 'left' | 'right') => {
    if (isAnimating || !safeCurrentJob) return;

    // Set animating flag
    setIsAnimating(true);

    // Store the job that's being swiped for potential undo
    setSwipedJobs(prev => [...prev, { job: safeCurrentJob, direction, index: currentIndex }]);

    // Determine final position with enhanced animation
    const xPosition = direction === 'left' ? -SCREEN_WIDTH * 1.5 : SCREEN_WIDTH * 1.5;
    const rotation = direction === 'left' ? '-30deg' : '30deg';
    const scale = 0.8;

    // Enhanced animation with rotation and scaling
    Animated.parallel([
      Animated.timing(swipeAnim, {
        toValue: { x: xPosition, y: -100 },
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(new Animated.Value(0), {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      })
    ]).start(() => {
      // Reset animation position
      swipeAnim.setValue({ x: 0, y: 0 });
      setSwipePosition({ x: 0, y: 0 });

      // Perform the action based on direction
      if (direction === 'right') {
        // Save to inbox
        handleSwipeRight();
        } else {
        // Move to next card
        setCurrentIndex(currentIndex + 1);
      }

      // Reset animation flag with a small delay to prevent immediate interaction
      setTimeout(() => {
      setIsAnimating(false);
      setSwipeOverlay(null);
      setSwipeTintColor('none');
      }, 50);
    });
  };

  // Disabled test job application creation to prevent database errors
  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     if (user && (jobs.length > 0 || mockJobs.length > 0)) {
  //       console.log('Creating test job application on mount...');
  //       createTestJobApplication();
  //     }
  //   }, 3000);
  //   return () => clearTimeout(timer);
  // }, [user, jobs]);

  // Save swiped job IDs to AsyncStorage for persistence across sessions (user-specific)
  const saveSwipedJobIds = async (jobIds: string[]) => {
    try {
      if (!user?.id) {
        console.log('No user logged in, skipping swiped job IDs save');
        return;
      }
      const userSpecificKey = `swipedJobIds_${user.id}`;
      await AsyncStorage.setItem(userSpecificKey, JSON.stringify(jobIds));
      console.log(`Saved ${jobIds.length} swiped job IDs to AsyncStorage for user ${user.id}`);
    } catch (error) {
      console.error('Failed to save swiped job IDs to AsyncStorage:', error);
    }
  };

  // Load swiped job IDs from AsyncStorage (user-specific)
  const loadSwipedJobIds = async (): Promise<string[]> => {
    try {
      if (!user?.id) {
        console.log('No user logged in, returning empty swiped job IDs');
        return [];
      }
      const userSpecificKey = `swipedJobIds_${user.id}`;
      const storedIds = await AsyncStorage.getItem(userSpecificKey);
      if (storedIds) {
        const ids = JSON.parse(storedIds) as string[];
        console.log(`Loaded ${ids.length} previously swiped job IDs from AsyncStorage for user ${user.id}`);
        return ids;
      }
      return [];
    } catch (error) {
      console.error('Failed to load swiped job IDs from AsyncStorage:', error);
      return [];
    }
  };

  // Function to reset swiped jobs when we're running low (user-specific)
  const resetSwipedJobs = async () => {
    try {
      if (!user?.id) {
        console.log('No user logged in, skipping swiped job reset');
        return [];
      }
      const userSpecificKey = `swipedJobIds_${user.id}`;
      await AsyncStorage.removeItem(userSpecificKey);
      console.log(`🔄 Reset swiped job history to show more options for user ${user.id}`);
      return [];
    } catch (error) {
      console.error('Error resetting swiped jobs:', error);
      return [];
    }
  };

  // Function to load personalized job recommendations
  const loadPersonalizedRecommendations = async (jobData: Job[]) => {
    if (!user?.id) {
      console.log('No user ID available for personalized recommendations');
      return jobData;
    }

    try {
      console.log('Loading personalized recommendations...');

      // Check if user has preferences
      const userPrefs = await getUserJobPreferences(user.id);
      setHasUserPreferences(!!userPrefs);

      if (!userPrefs) {
        console.log('No user preferences found, showing all jobs');
        return jobData;
      }

      // Get personalized recommendations
      const result = await getPersonalizedJobRecommendations(user.id, jobData);
      setMatchingResult(result);
      setRecommendations(result.recommendations);

      console.log(`Generated ${result.recommendations.length} personalized recommendations from ${result.total_jobs_analyzed} jobs`);

      // Return the recommended jobs in order of score
      return result.recommendations.map(rec => rec.job);

    } catch (error) {
      console.error('Error loading personalized recommendations:', error);
      // Fallback to showing all jobs
      return jobData;
    }
  };

  // Pull to refresh handler
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadJobs(true); // Force refresh
    } finally {
      setRefreshing(false);
    }
  };

  // Search handler
  const handleSearch = async (query: string) => {
    if (!query.trim()) return;

    setSearchLoading(true);
    try {
      console.log(`[SEARCH] Searching for: "${query}"`);
      const searchResults = await searchJobs(query, true);

      if (searchResults.length > 0) {
        setJobs(searchResults);
        setFilteredJobs(searchResults);
        setCurrentIndex(0);
        console.log(`[SEARCH] Found ${searchResults.length} jobs for query: "${query}"`);
      } else {
        console.log(`[SEARCH] No jobs found for query: "${query}"`);
      }
    } catch (error) {
      console.error('[SEARCH] Search failed:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Removed old refine filters handler - using new filter system

  // Add a ref to track if we're already loading jobs to prevent concurrent calls
  const isLoadingJobsRef = useRef(false);

  // Function to load jobs - defined outside useEffect so it can be called from elsewhere
  const loadJobs = async (forceReset = false) => {
    // Prevent concurrent calls to loadJobs
    if (isLoadingJobsRef.current) {
      console.log('[JOBS] Jobs already loading, skipping duplicate call');
      return;
    }

    // Safety check: ensure we have a user before proceeding
    if (!user?.id) {
      console.log('[JOBS] No user available, skipping job loading');
      setIsLoading(false);
      return;
    }

    console.log(`[JOBS] Starting loadJobs (forceReset: ${forceReset})`);
    isLoadingJobsRef.current = true;
    setIsLoading(true);
    
    // Set a 10-second timeout to automatically stop loading
    const loadingTimeout = setTimeout(() => {
      console.log('[JOBS] Loading timeout reached (10 seconds), stopping loading state');
      setIsLoading(false);
      isLoadingJobsRef.current = false;
    }, 10000);
    try {
      console.log('[JOBS] Calling personalized job search...');

      // Use personalized search if user has preferences, otherwise fallback to regular fetch
      let jobData;
      let upgradeRequired = false;
      let searchInfo = null;

      if (user?.id) {
        console.log('[JOBS] Fetching personalized jobs for user:', user.id);
        const result = await fetchJobsForUser(user.id, 100, forceReset);

        jobData = result.jobs;
        upgradeRequired = result.upgradeRequired || false;
        searchInfo = {
          searchesUsed: result.searchesUsed,
          searchLimit: result.searchLimit,
          cacheUsed: result.cacheUsed
        };

        // Update queue stats
        if (result.queueStats) {
          setQueueStats(result.queueStats);
        }

        // Show upgrade modal if rate limit reached
        if (upgradeRequired) {
          console.log(`[JOBS] Rate limit reached: ${result.searchesUsed}/${result.searchLimit} searches used`);
          setUpgradeInfo({
            searchesUsed: result.searchesUsed || 0,
            searchLimit: result.searchLimit || 5
          });
          setShowUpgradeModal(true);
        }
      } else {
        console.log('[JOBS] No authenticated user - jobs should only load after onboarding is complete');
        isLoadingJobsRef.current = false;
        setIsLoading(false);
        return;
      }

      console.log(`[JOBS] Successfully loaded ${jobData.length} jobs from API${searchInfo?.cacheUsed ? ' (cached)' : ''}`);

      // Apply personalized recommendations
      const personalizedJobs = await loadPersonalizedRecommendations(jobData);

      // Load previously swiped job IDs from AsyncStorage
      let persistedSwipedJobIds = await loadSwipedJobIds();

      // Combine with current session's swiped job IDs
      const currentSwipedJobIds = swipedJobs.map(sj => sj.job.id);
      let allSwipedJobIds = [...new Set([...persistedSwipedJobIds, ...currentSwipedJobIds])];

      // Check if we're running out of jobs (less than 5 left)
      let newJobs = personalizedJobs.filter(job => !allSwipedJobIds.includes(job.id));

      // If we have fewer than 5 jobs left, reset swiped jobs history
      if (newJobs.length < 5 || forceReset) {
        await resetSwipedJobs();
        allSwipedJobIds = []; // Clear swiped jobs in memory
        newJobs = jobData; // Use all jobs
        console.log('⚠️ Running low on new jobs - resetting job history');
      }

      console.log(`After processing swiped jobs (${allSwipedJobIds.length} total), ${newJobs.length} jobs available`);

      // Filter jobs to ensure they have description and picture before storing
      const displayReadyJobs = filterJobsForDisplay(newJobs);
      console.log(`Filtered ${newJobs.length} jobs to ${displayReadyJobs.length} display-ready jobs`);

      // Update state with the fetched jobs, preserving current jobs if this is a refresh
      setJobs(prevJobs => {
        // Combine previous and new jobs, removing duplicates
        const combinedJobs = [...prevJobs, ...displayReadyJobs];
        const uniqueJobs = Array.from(new Map(combinedJobs.map(job => [job.id, job])).values());
        return uniqueJobs;
      });

      // Update filtered jobs, preserving current filtered jobs
      setFilteredJobs(prevFiltered => {
        // If we're starting fresh (index is 0), use all new jobs
        if (currentIndex === 0) {
          return displayReadyJobs;
        }
        // Otherwise, append new jobs to existing filtered jobs
        return [...prevFiltered, ...displayReadyJobs];
      });
    } catch (error) {
      console.error('[JOBS] Error loading jobs:', error);
      console.log('[JOBS] Loading fallback jobs for testing');

      try {
        // Load fallback jobs from the JSON file
        const fallbackJobs = require('../../jobs-fallback.json');
        
        // Transform fallback jobs to match the expected Job interface
        const transformedJobs = fallbackJobs.map((job: any) => ({
          id: job.id,
          title: job.title,
          company: job.company,
          location: job.location,
          pay: job.salary,
          image: job.logo,
          logo: job.logo,
          distance: 'N/A',
          tags: job.qualifications || [],
          description: job.description,
          qualifications: job.qualifications || [],
          requirements: job.requirements || [],
          type: 'Full-time',
          experienceLevel: 'Mid-level',
          skills: job.qualifications || []
        }));

        console.log(`[JOBS] Loaded ${transformedJobs.length} fallback jobs`);

        // Filter fallback jobs to ensure they meet display requirements
        const displayReadyJobs = filterJobsForDisplay(transformedJobs);
        console.log(`[JOBS] ${displayReadyJobs.length} fallback jobs are display-ready`);

        // Update state with fallback jobs
        setJobs(prevJobs => {
          const combinedJobs = [...prevJobs, ...displayReadyJobs];
          const uniqueJobs = Array.from(new Map(combinedJobs.map(job => [job.id, job])).values());
          return uniqueJobs;
        });

        setFilteredJobs(prevFiltered => {
          // If we're starting fresh (index is 0), use fallback jobs
          if (currentIndex === 0) {
            return displayReadyJobs;
          }
          // Otherwise, append fallback jobs to existing filtered jobs
          return [...prevFiltered, ...displayReadyJobs];
        });
      } catch (fallbackError) {
        console.error('[JOBS] Error loading fallback jobs:', fallbackError);
        // If even fallback fails, keep existing jobs
        setJobs(prevJobs => {
          const combinedJobs = [...prevJobs];
          const uniqueJobs = Array.from(new Map(combinedJobs.map(job => [job.id, job])).values());
          return uniqueJobs;
        });

        setFilteredJobs(prevFiltered => {
          return prevFiltered; // No new jobs to add
        });
      }
    } finally {
      console.log('[JOBS] loadJobs completed, resetting loading flags');
      clearTimeout(loadingTimeout);
      setIsLoading(false);
      isLoadingJobsRef.current = false;
    }
  };

  // Filter handlers
  const handleApplyFilters = async (newFilters: JobFilters) => {
    try {
      setJobFilters(newFilters);
      if (user?.id) {
        await saveFilterSettings(user.id, newFilters);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      // Still apply filters locally even if saving fails
      setJobFilters(newFilters);
    }
  };



  const handleLocationSelect = (location: string) => {
    try {
      const newFilters = {
        ...jobFilters,
        location: location
      };
      handleApplyFilters(newFilters);
    } catch (error) {
      console.error('Error selecting location:', error);
    }
  };

  // Removed old location settings handler - using new filter system

  // Initialize filters and user location
  useEffect(() => {
    const initializeFilters = async () => {
      if (user?.id) {
        console.log(`[STARTUP] Initializing filters for user: ${user.id}`);

        // Check if user has completed filter onboarding (user-specific)
        try {
          const savedPreferences = await AsyncStorage.getItem(`filterPreferences_${user.id}`);
          console.log(`[STARTUP] Saved preferences from AsyncStorage:`, savedPreferences ? 'Found' : 'Not found');

          if (savedPreferences) {
            // Verify that the user also has database preferences
            const { data: dbPrefs } = await supabase
              .from('user_job_preferences')
              .select('*')
              .eq('user_id', user.id)
              .maybeSingle();

            if (dbPrefs) {
              // Both AsyncStorage and database have preferences
              const preferences: FilterPreferences = JSON.parse(savedPreferences);
              setFilterPreferences(preferences);
              setHasCompletedFilters(true);
            } else {
              // AsyncStorage has preferences but database doesn't - clear AsyncStorage
              console.log('[STARTUP] AsyncStorage has preferences but database does not, clearing cache');
              await AsyncStorage.removeItem(`filterPreferences_${user.id}`);
              setHasCompletedFilters(false);
              return; // Show filter onboarding
            }

            // Load saved filter settings (more lenient)
            const savedFilters = await loadFilterSettings(user.id);
            const lenientFilters = {
              ...savedFilters,
              minSalary: 0,
              maxSalary: 500000,
              hasLogo: false,
              quickFilters: {
                ...savedFilters.quickFilters,
                hasLogo: false
              }
            };
            setJobFilters(lenientFilters);
            console.log('[STARTUP] Applied saved preferences from AsyncStorage');

            // Load jobs with the saved preferences
            setTimeout(() => {
              loadJobs().catch(error => {
                console.error('[STARTUP] Error loading jobs with saved preferences:', error);
                setIsLoading(false);
              });
            }, 100);
          } else {
            // Check database for user preferences
            try {
              const { data: userPrefs, error } = await supabase
                .from('user_job_preferences')
                .select('*')
                .eq('user_id', user.id)
                .maybeSingle(); // Use maybeSingle to avoid errors

              if (userPrefs && !error) {
                console.log('[STARTUP] Found user preferences in database, converting to local format');

                // Convert database preferences to local format
                const preferences: FilterPreferences = {
                  location: userPrefs.preferred_locations?.[0] || 'Remote',
                  workStyle: userPrefs.remote_work_preference === 'required' ? 'remote' :
                            userPrefs.remote_work_preference === 'preferred' ? 'hybrid' : 'in-person',
                  jobTypes: userPrefs.preferred_job_types || [],
                  experienceLevel: userPrefs.experience_level || '',
                  salaryRange: {
                    min: userPrefs.min_salary || 0,
                    max: userPrefs.max_salary || 200000
                  },
                  jobCategories: userPrefs.preferred_roles || [],
                  completed: true
                };

                // Save to AsyncStorage for faster future loads (user-specific)
                await AsyncStorage.setItem(`filterPreferences_${user.id}`, JSON.stringify(preferences));
                setFilterPreferences(preferences);
                setHasCompletedFilters(true);

                // Load jobs with the database preferences
                setTimeout(() => {
                  loadJobs().catch(error => {
                    console.error('[STARTUP] Error loading jobs with database preferences:', error);
                    setIsLoading(false);
                  });
                }, 100);

                // Apply lenient filters
                const lenientFilters: JobFilters = {
                  ...defaultFilters,
                  location: preferences.location,
                  remoteOnly: preferences.workStyle === 'remote',
                  inPersonOnly: preferences.workStyle === 'in-person',
                  employmentTypes: preferences.jobTypes.length > 0 ? preferences.jobTypes : [],
                  experienceLevel: preferences.experienceLevel ? [preferences.experienceLevel] : [],
                  minSalary: 0,
                  maxSalary: 500000,
                  jobCategories: [],
                  hasLogo: false,
                  quickFilters: {
                    remote: preferences.workStyle === 'remote',
                    recentlyPosted: false,
                    hasLogo: false
                  }
                };
                setJobFilters(lenientFilters);
                console.log('[STARTUP] Applied database preferences to filters');
              } else {
                console.log('[STARTUP] No user preferences found in database, clearing AsyncStorage and showing filter onboarding');
                // Clear any cached preferences since database has none
                await AsyncStorage.removeItem(`filterPreferences_${user.id}`);
                setHasCompletedFilters(false);
                return; // Show filter onboarding
              }
            } catch (dbError) {
              console.error('[STARTUP] Error loading preferences from database:', dbError);
              console.log('[STARTUP] Database error, showing filter onboarding');
              setHasCompletedFilters(false);
              return; // Show filter onboarding
            }
          }
        } catch (error) {
          console.error('Error loading filter preferences:', error);
          console.log('[STARTUP] AsyncStorage error, showing filter onboarding');
          setHasCompletedFilters(false);
          return;
        }

        // Get user's current location
        const location = await getUserLocation();
        if (location) {
          // In a real app, you'd use reverse geocoding to get the city name
          setUserLocation(`Current Location`);
        }
      }
    };

    initializeFilters();
  }, [user?.id]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log(`[DEBUG] hasCompletedFilters changed to: ${hasCompletedFilters}`);
  }, [hasCompletedFilters]);

  // Apply filters when jobs or filters change
  useEffect(() => {
    const applyFiltersAsync = async () => {
      if (jobs.length > 0) {
        try {
          // Use basic filtering - location is handled in the new filter system
          const filtered = applyFilters(jobs, jobFilters);
          setFilteredJobs(filtered);
          console.log(`[FILTERS] Applied filters, ${filtered.length} jobs match criteria`);
        } catch (error) {
          console.error('[FILTERS] Error applying filters:', error);
          // Fallback to basic filtering with error handling
          try {
            const filtered = applyFilters(jobs, jobFilters);
            setFilteredJobs(filtered);
            console.log(`[FILTERS] Applied fallback basic filters, ${filtered.length} jobs match criteria`);
          } catch (fallbackError) {
            console.error('[FILTERS] Fallback filtering also failed:', fallbackError);
            // Last resort: show all jobs
            setFilteredJobs(jobs);
            console.log(`[FILTERS] Showing all jobs due to filter errors`);
          }
        }
      }
    };

    applyFiltersAsync().catch(error => {
      console.error('[FILTERS] Async filter application failed:', error);
      // Emergency fallback: show all jobs
      setFilteredJobs(jobs);
    });
  }, [jobs, jobFilters]);

  // Removed old location settings initialization - using new filter system

  // Set up background refresh listeners (but don't auto-load jobs on mount)
  useEffect(() => {
    console.log('[JOBS] Setting up background refresh listeners...');

    // Set up AppState listener to refresh when app comes to foreground
    const appStateSubscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active' && jobs.length > 0) {
        console.log('[JOBS] App has come to the foreground, refreshing jobs...');
        loadJobs();
      }
    });

    // Set up a refresh interval to keep job data fresh (only if jobs are already loaded)
    const refreshInterval = setInterval(() => {
      if (jobs.length > 0) {
        console.log('[JOBS] Auto-refreshing jobs data...');
        loadJobs();
      }
    }, 600000); // 10 minutes

    // Cleanup subscriptions on unmount
    return () => {
      console.log('[JOBS] Component unmounting, cleaning up subscriptions...');
      appStateSubscription.remove();
      clearInterval(refreshInterval);
    };
  }, [jobs.length]); // Depend on jobs.length to only refresh when we have jobs

  // Removed loadMessages function - simplified to browse only

  // Note: Applications are loaded in the JobsScreenWrapper component
  // This component receives them as props (userApplications)

  // Reset animation values when current index changes (when we move to a new card)
  React.useEffect(() => {
    // Reset all animation values and states when moving to a new card
    swipeAnim.setValue({x: 0, y: 0});
    setSwipeOverlay(null);
    setSwipeTintColor('none');
    setIsAnimating(false);

    // Reset modal animations to ensure clean state for next job
    modalSlideAnim.setValue(SCREEN_HEIGHT);
    backdropOpacityAnim.setValue(0);
    modalDragY.setValue(0);
    setIsDragging(false);
  }, [currentIndex]);

  // Show filter onboarding if not completed
  // Removed excessive render logging to improve performance during swiping
  if (!hasCompletedFilters) {
    return (
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error('[ONBOARDING] Filter onboarding crashed:', error, errorInfo);
          // Fallback: skip onboarding and go to main app
          setHasCompletedFilters(true);
          setTimeout(() => loadJobs(), 100);
        }}
      >
        <FilterOnboardingFlow
          onComplete={handleFilterOnboardingComplete}
          onSkip={handleSkipFilterOnboarding}
        />
      </ErrorBoundary>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background, paddingBottom: insets.bottom || 16 }]}>
        <StatusBar barStyle={theme === 'light' ? 'dark-content' : 'light-content'} />

      {/* Header with JOBS title */}
      <View style={[styles.tabsHeader, { paddingVertical: 16, marginBottom: 10 }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={[styles.headerTitle, { color: themeColors.text, fontSize: 24, letterSpacing: 1 }]}>JOBS</Text>
          {queueStats && (
            <View style={{
              marginLeft: 12,
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
              backgroundColor: queueStats.queue_status === 'healthy' ? '#4CAF50' :
                             queueStats.queue_status === 'low' ? '#FF9800' :
                             queueStats.queue_status === 'critical' ? '#F44336' : '#9E9E9E'
            }}>
              <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                {queueStats.available_jobs} jobs
              </Text>
            </View>
          )}
        </View>
        <View style={{ flexDirection: 'row' }}>
          {/* Removed old refine filters button - using new filter system */}
          <TouchableOpacity
            style={[styles.refreshButton, { marginRight: 10 }]}
            onPress={resetAnimationState}
            onLongPress={() => {
              console.log('[DEBUG] Animation state - isAnimating:', isAnimating, 'isSwiping:', isSwiping);
            }}
          >
            <MaterialIcons name="refresh" size={20} color={isAnimating ? '#FF5252' : themeColors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.refreshButton, { marginRight: 10 }]}
            onPress={handleRefresh}
          >
            <FontAwesome name="refresh" size={20} color={themeColors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.refreshButton, { marginRight: 10 }]}
            onPress={() => setShowSearchBar(!showSearchBar)}
          >
            <MaterialIcons name="search" size={20} color={showSearchBar ? themeColors.tint : themeColors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      {showSearchBar && (
        <SearchBar
          onSearch={handleSearch}
          loading={searchLoading}
          onClear={() => {
            setSearchQuery('');
            // Reset to original jobs
            if (jobs.length > 0) {
              setFilteredJobs(jobs);
              setCurrentIndex(0);
            }
          }}
        />
      )}

      {/* Header removed - title and subtitle eliminated */}

      {/* User instruction hint - updated text */}
      <BlurView
        tint={theme === 'dark' ? 'dark' : 'light'}
        intensity={80}
        style={[styles.swipeHintContainer, { bottom: 20 }]}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
          <MaterialCommunityIcons name="gesture-swipe-right" size={20} color={themeColors.tint} style={{ marginRight: 5 }} />
          <Text style={{ color: themeColors.text, fontSize: 14 }}>Swipe right to apply</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginTop: 5 }}>
          <MaterialCommunityIcons name="gesture-swipe-left" size={20} color={themeColors.textSecondary} style={{ marginRight: 5 }} />
          <Text style={{ color: themeColors.text, fontSize: 14 }}>Swipe left to pass</Text>
        </View>
      </BlurView>

      {/* Removed old filter button - using new card-based filter system */}





      {/* Main Jobs View - Simplified */}
      <View style={styles.contentContainer}>
        {/* Card Viewing Area - Remove constraining containers */}
        <View style={styles.cardViewingArea}>
          {/* Cards will render here without constraints */}
        </View>

          {/* Bottom action buttons */}
          <View style={[
            styles.buttonsContainer,
            {
              marginTop: 8,
              marginBottom: insets.bottom ? insets.bottom + 6 : 16,
              zIndex: 3000,
              elevation: 3000,
              position: 'absolute',
              bottom: insets.bottom ? insets.bottom + 6 : 16
            }
          ]}>
            <TouchableOpacity
              style={[styles.button, styles.undoButton, { backgroundColor: themeColors.card }]}
              onPress={handleUndoSwipe}
              activeOpacity={0.7}
              disabled={swipedJobs.length === 0}
              onPressIn={() => setUndoActive(true)}
              onPressOut={() => setUndoActive(false)}
            >
              <Animated.View style={[styles.buttonInner, { borderColor: theme === 'dark' ? '#222' : '#FFC107', opacity: swipedJobs.length === 0 ? 0.4 : 1, backgroundColor: undoActive ? (theme === 'dark' ? '#FFC107' : '#FFF8E1') : 'transparent' }] }>
                <FontAwesome name="undo" size={24} color={undoActive ? (theme === 'dark' ? '#222' : '#FFC107') : '#fff'} />
              </Animated.View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.passButton, { backgroundColor: themeColors.card }]}
              onPress={handleButtonSwipeLeft}
              activeOpacity={0.7}
              onPressIn={() => { 
                console.log('[DEBUG] Pass button onPressIn triggered');
                animateButton(passScale, 0.92); 
                setPassActive(true); 
              }}
              onPressOut={() => { 
                console.log('[DEBUG] Pass button onPressOut triggered');
                animateButton(passScale, 1); 
                setPassActive(false); 
              }}
            >
              <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : redColor, transform: [{ scale: passScale }], backgroundColor: passActive ? (theme === 'dark' ? '#FF5252' : '#FFEBEE') : 'transparent' }] }>
                <FontAwesome name="close" size={26} color={passActive ? iconColorActive : iconColorInactive} />
              </Animated.View>
            </TouchableOpacity>
            <TouchableOpacity
            style={[styles.button, styles.infoButton, { backgroundColor: themeColors.card }]}
            onPress={() => {
              console.log('Info button pressed, current job:', safeCurrentJob?.title);
              if (safeCurrentJob) {
                openJobDetails(safeCurrentJob);
              } else {
                console.warn('No current job available for info button');
              }
            }}
            activeOpacity={0.7}
            onPressIn={() => { animateButton(infoScale, 0.92); setInfoActive(true); }}
            onPressOut={() => { animateButton(infoScale, 1); setInfoActive(false); }}
          >
            <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : blueColor, transform: [{ scale: infoScale }], backgroundColor: infoActive ? (theme === 'dark' ? '#42A5F5' : '#E3F2FD') : 'transparent' }] }>
              <FontAwesome name="info-circle" size={26} color={infoActive ? iconColorActive : iconColorInactive} />
            </Animated.View>
          </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.likeButton, { backgroundColor: themeColors.card }]}
              onPress={handleButtonSwipeRight}
              activeOpacity={0.7}
              onPressIn={() => { 
                console.log('[DEBUG] Apply button onPressIn triggered');
                animateButton(likeScale, 0.92); 
                setLikeActive(true); 
              }}
              onPressOut={() => { 
                console.log('[DEBUG] Apply button onPressOut triggered');
                animateButton(likeScale, 1); 
                setLikeActive(false); 
              }}
            >
              <Animated.View style={[styles.buttonInner, {borderColor: theme === 'dark' ? '#222' : greenColor, transform: [{ scale: likeScale }], backgroundColor: likeActive ? (theme === 'dark' ? '#4CAF50' : '#E8F5E9') : 'transparent' }] }>
                <FontAwesome name="check-circle" size={26} color={likeActive ? iconColorActive : iconColorInactive} />
              </Animated.View>
            </TouchableOpacity>
          </View>
        </View>

      {/* Search Modal */}
      <Modal
        visible={isSearchVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsSearchVisible(false)}
      >
        {renderSearchModal()}
      </Modal>

      {/* Enhanced Job Details Modal */}
      {renderJobDetailsModal()}

      {/* Applied Notification */}
      {showAppliedNotification && (
        <View style={[styles.appliedNotification, { backgroundColor: themeColors.card }]}>
          <FontAwesome name="check-circle" size={24} color={theme === 'dark' ? '#000' : themeColors.text} />
          <Text style={[styles.appliedNotificationText, { color: theme === 'dark' ? '#fff' : themeColors.text }]}>Applied Successfully!</Text>
        </View>
      )}

      {/* Render cards at root level to allow free movement over all UI elements */}
      <ScrollView
        style={styles.cardsOverlay}
        contentContainerStyle={{ 
          flex: 1, 
          justifyContent: 'center',
          alignItems: 'center'
          }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={themeColors.tint}
              colors={[themeColors.tint]}
            />
          }
          scrollEnabled={false} // Disable scrolling to maintain card swipe functionality
        >
        {renderCards()}
      </ScrollView>

      {/* Filter Modals */}
      {/* Removed old filter modals - using new card-based filter system */}

      {/* Enhanced Modals */}
      <UpgradeModal
        visible={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        searchesUsed={upgradeInfo.searchesUsed}
        searchLimit={upgradeInfo.searchLimit}
        onUpgrade={(plan) => {
          console.log(`[UPGRADE] User selected ${plan} plan`);
          // TODO: Navigate to paywall/subscription screen
        }}
      />
      </SafeAreaView>
  );
}

export default JobsScreenWrapper;

// Define styles for components
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    overflow: 'visible', // Ensure container doesn't clip cards during swiping
  },

  headerContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 8,
    zIndex: 1500, // Higher z-index to prevent default card overlap
  },
  // Enhanced overlay styles for swipe actions - much more visible
  overlayBadge: {
    position: 'absolute',
    padding: 20,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.5,
    shadowRadius: 15,
    elevation: 15,
    zIndex: 200,
    borderWidth: 3,
    borderColor: '#fff',
  },
  likeBadge: {
    backgroundColor: '#00C851', // Brighter green
    top: 80,
    right: 30,
    transform: [{ rotate: '15deg' }, { scale: 1.2 }],
    paddingVertical: 20,
    paddingHorizontal: 25,
  },
  passBadge: {
    backgroundColor: '#FF4444', // Brighter red
    top: 80,
    left: 30,
    transform: [{ rotate: '-15deg' }, { scale: 1.2 }],
    paddingVertical: 20,
    paddingHorizontal: 25,
  },
  overlayText: {
    color: '#FFF',
    fontSize: 20,
    fontWeight: '900',
    marginTop: 6,
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.7)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 16,
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    top: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  weekDayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  dayButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 6,
  },
  selectedDayButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  dayText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  arrowButton: {
    position: 'absolute',
    right: 10,
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardViewingArea: {
    flex: 1,
    // Remove all constraints to allow cards to move freely
  },
  cardsOverlay: {
    position: 'absolute',
    top: 120, // Reduced top margin to prevent cutoff
    left: 0,
    right: 0,
    bottom: 120, // Reduced bottom margin to prevent cutoff
    pointerEvents: 'box-none', // Allow touches to pass through to underlying elements when not touching cards
    zIndex: 900, // Below action buttons but above content
  },
  progressContainer: {
    marginHorizontal: 20,
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
  },
  progressDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressPercent: {
    fontSize: 12,
    marginRight: 10,
  },
  progressTextContainer: {
    flex: 1,
  },
  progressCount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressLabel: {
    fontSize: 12,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconSpacer: {
    width: 10,
  },
  segmentedPillContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 24,
    padding: 6,
    borderWidth: 0,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 2000,
    zIndex: 2000,
    width: '100%',
  },
  segmentedPillIndicator: {
    position: 'absolute',
    top: 4,
    bottom: 4,
    left: 4,
    borderRadius: 20,
    backgroundColor: '#fff', // overridden inline
    zIndex: 1,
    elevation: 1,
  },
  segmentedPillButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 20,
    zIndex: 2,
    elevation: 2,
  },
  segmentedPillText: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  // Removed duplicate sectionTitle - keeping the one defined later for job details
  sectionSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.8,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 0,
    paddingHorizontal: 0,
    marginTop: 0,
    marginBottom: 0,
    backgroundColor: 'transparent',
    borderRadius: 18,
    zIndex: 5,
    elevation: 5,
  },
  searchInputModern: {
    flex: 1,
    height: 44,
    paddingVertical: 0,
    fontSize: 15,
    fontWeight: '500',
    backgroundColor: 'transparent',
  },
  filterIconButton: {
    width: 42,
    height: 42,
    borderRadius: 21,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0,
    backgroundColor: 'transparent',
    marginLeft: 2,
  },
  tabSelector: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
    overflow: 'visible', // Ensure content container doesn't clip cards
  },
  tabsHeader: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 12,
    alignItems: 'center',
  },
  filterButton: {
    width: 46,
    height: 46,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  // Removed inbox-related styles - simplified to browse only
  listContent: {
    paddingVertical: 8,
    paddingBottom: 120,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 60,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  searchModalContainer: {
    padding: 16,
    borderRadius: 12,
  },
  jobDetailsModalContainer: {
    padding: 16,
    borderRadius: 12,
  },
  jobDetailsImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  jobDetailsInfo: {
    padding: 16,
  },
  jobDetailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  jobDetailsLocation: {
    fontSize: 14,
    marginBottom: 4,
  },
  jobDetailsPay: {
    fontSize: 14,
    marginBottom: 8,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
    letterSpacing: 0.2,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  aiExplanationContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  aiExplanationIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  paragraphText: {
    fontSize: 15,
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  bulletListContainer: {
    marginTop: 8,
  },
  bulletItem: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  bullet: {
    fontSize: 15,
    marginRight: 8,
    fontWeight: 'bold',
  },
  bulletText: {
    fontSize: 15,
    flex: 1,
    lineHeight: 22,
  },
  jobDetailsDescription: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  applyButton: {
    padding: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    color: '#fff',
  },
  // Enhanced modal panel styles
  enhancedModalPanel: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: SCREEN_HEIGHT * 0.9,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
    zIndex: 1000,
  },
  enhancedDragHandle: {
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  dragIndicator: {
    width: 40,
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
  },
  dragHint: {
    fontSize: 12,
    fontWeight: '500',
  },
  enhancedPanelContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  cardsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingBottom: 24,
  },
  card: {
    position: 'absolute',
    width: SCREEN_WIDTH, // Full width for full-sized panels
    height: CARD_HEIGHT,
    borderRadius: 32, // More rounded corners
    overflow: 'hidden',
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 }, // Enhanced shadow for better depth
    shadowOpacity: 0.2, // Slightly stronger shadow
    shadowRadius: 16, // Larger shadow radius for softer effect
    elevation: 12, // Higher elevation for better depth on Android
    borderWidth: 0,
    zIndex: 1000, // Lower default z-index, will be overridden during swiping
  },
  nextCardStyle: {
    transform: [{ scale: 0.95 }],
    top: 10,
    zIndex: 999, // Lower than current card and below buttons by default
  },
  cardInner: {
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '50%',
    justifyContent: 'flex-end',
    padding: 20,
  },
  cardInfo: {
    padding: 20,
  },
  topInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distanceText: {
    color: '#fff',
    marginLeft: 5,
  },
  cardTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 5,
  },
  cardLocation: {
    color: '#fff',
    fontSize: 16,
    marginTop: 5,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  tag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
    marginRight: 5,
    marginBottom: 5,
  },
  tagText: {
    color: '#fff',
    fontSize: 12,
  },
  buttonsContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center', // Center buttons
    alignItems: 'center',
    gap: 18, // Increased gap for better spacing with larger cards
    padding: 12, // Slightly increased padding
    marginTop: 8, // Small margin to separate from cards
    zIndex: 3000, // Ensure buttons stay above panels
    elevation: 3000,
  },
  button: {
    width: 64, // Slightly larger for better touch target
    height: 64, // Slightly larger
    borderRadius: 32, // Adjusted border radius
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 }, // Enhanced shadow
    shadowOpacity: 0.15, // Stronger shadow
    shadowRadius: 6, // Larger shadow radius
    elevation: 4, // Higher elevation
  },
  buttonInner: {
    width: 52, // Slightly larger for better proportion
    height: 52, // Slightly larger
    borderRadius: 26, // Adjusted border radius
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    marginHorizontal: 8, // Maintained margin
  },
  passButton: {
    backgroundColor: '#fff',
  },
  infoButton: {
    backgroundColor: '#fff',
  },
  likeButton: {
    backgroundColor: '#fff',
  },
  undoButton: {
    marginRight: 18,
    shadowColor: '#FFC107',
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 2,
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  noJobsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  noJobsText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  clearButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  appliedNotification: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    gap: 10,
  },
  appliedNotificationText: {
    fontSize: 16,
    fontWeight: '600',
  },
  notificationContainer: {
    position: 'absolute',
    top: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 100,
  },
  notification: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  filterInput: {
    height: 40,
    borderColor: '#ddd',
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  cardImageHalfWrapper: {
    width: '100%',
    height: '55%', // Increased image area for more visual impact
    borderTopLeftRadius: 32, // Match updated card border radius
    borderTopRightRadius: 32,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#f5f5f5',
  },
  cardImageHalf: {
    width: '100%',
    height: '100%',
  },
  logoOverlayHalf: {
    position: 'absolute',
    top: 20, // Increased spacing from top
    left: 20, // Increased spacing from left
    backgroundColor: '#fff',
    borderRadius: 36, // Larger border radius for bigger logo
    padding: 6, // Increased padding
    zIndex: 8,
    shadowColor: '#000',
    shadowOpacity: 0.15, // Stronger shadow
    shadowRadius: 8, // Larger shadow radius
    elevation: 6, // Higher elevation
  },
  companyLogo: {
    width: 56, // Larger logo for better visibility
    height: 56,
    borderRadius: 28,
  },
  cardDetailsHalf: {
    width: '100%',
    height: '45%', // Adjusted to match image area change
    borderBottomLeftRadius: 32, // Match updated card border radius
    borderBottomRightRadius: 32,
    padding: 24, // Increased padding for better spacing with larger cards
    zIndex: 3,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 10,
    justifyContent: 'flex-start', // Align content to top for better hierarchy
  },
  cardTitleModern: {
    color: '#333',
    fontSize: 22, // Increased font size for larger cards
    fontWeight: '700', // Bolder font
    marginBottom: 8, // Increased margin for better spacing
    letterSpacing: 0.3, // Slightly more letter spacing for modern typography
    lineHeight: 28, // Better line height for readability
  },
  cardLocationModern: {
    color: '#666',
    fontSize: 15, // Slightly larger for better readability
    marginBottom: 12, // Increased margin for better spacing
    fontWeight: '500', // Medium weight for better hierarchy
    lineHeight: 20, // Better line height
  },
  tagsContainerModern: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16, // Increased margin for better spacing with larger cards
    gap: 6, // Increased gap for better spacing
  },
  tagModern: {
    backgroundColor: 'rgba(66, 133, 244, 0.1)', // Slightly more opaque for better visibility
    paddingHorizontal: 14, // Increased padding for larger cards
    paddingVertical: 7, // Increased padding
    borderRadius: 18, // More rounded corners for modern look
    marginRight: 8,
    marginBottom: 8,
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  tagTextModern: {
    color: '#4285F4',
    fontSize: 13, // Slightly larger for better readability
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  payContainerModern: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: 'rgba(66, 133, 244, 0.1)', // Slightly more opaque
    paddingHorizontal: 12, // Increased padding
    paddingVertical: 8, // Increased padding
    borderRadius: 14, // More rounded
    alignSelf: 'flex-start',
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  payModern: {
    color: '#333',
    fontWeight: '700',
    fontSize: 19, // Slightly larger for better visibility
    opacity: 0.9, // Slight opacity for contrast with title
    letterSpacing: 0.2,
  },
  distanceModern: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    top: 24, // Increased spacing from top
    right: 24, // Increased spacing from right
    backgroundColor: 'rgba(255,255,255,0.9)', // More opaque for better visibility
    paddingHorizontal: 12, // Increased padding
    paddingVertical: 8, // Increased padding
    borderRadius: 18, // More rounded
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15, // Stronger shadow
    shadowRadius: 6, // Larger shadow radius
    elevation: 3, // Higher elevation
  },
  distanceTextModern: {
    color: '#333',
    marginLeft: 6,
    fontSize: 13, // Slightly larger for better readability
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  aiScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  aiScoreText: {
    fontSize: 11,
    fontWeight: '600',
    marginLeft: 4,
    flex: 1,
  },
  aiScoreBadge: {
    fontSize: 12,
    marginLeft: 4,
  },
  overlayContainerModern: {
    position: 'absolute',
    top: 40,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 12,
  },
  overlayBadgeModern: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 30,
    borderWidth: 3,
    marginBottom: 10,
    shadowOffset: { width: 0, height: 4 },
  },
  overlayTextModern: {
    fontSize: 22,
    fontWeight: 'bold',
    marginLeft: 12,
  },

  // Modern card styles for enhanced UI
  cardImageWrapper: {
    width: '100%',
    height: '50%',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  cardCompany: {
    fontSize: 14,
    color: '#888',
    marginBottom: 4,
  },
  companyBadge: {
    position: 'absolute',
    bottom: -24, // Adjusted for larger image area
    left: 24, // Increased spacing from left
    backgroundColor: '#fff',
    borderRadius: 24, // Larger border radius
    padding: 3, // Slightly increased padding
    borderWidth: 3, // Thicker border for better definition
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 }, // Enhanced shadow
    shadowOpacity: 0.15, // Stronger shadow
    shadowRadius: 6, // Larger shadow radius
    elevation: 5, // Higher elevation
    zIndex: 10,
  },
  cardDetails: {
    padding: 16,
    paddingTop: 24,
    flex: 1,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  // Company information layout
  companyInfoContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  jobTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  jobTagPill: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  jobTagText: { // Renamed from tagText to avoid duplication
    fontSize: 12,
    fontWeight: '500',
  },
  jobMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  jobSalaryText: {
    fontSize: 16,
    fontWeight: '600',
  },
  jobDistanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  jobDistanceText: {
    fontSize: 13,
    marginLeft: 4,
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0,
    backgroundColor: 'transparent',
    marginRight: 10,
  },

  // Tinder-like details panel styles
  tinderDetailsPanel: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 20,
    zIndex: 1900, // Below action buttons but above job cards
  },
  panelDragHandle: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  panelHandle: {
    width: 40,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(0,0,0,0.2)',
    marginBottom: 8,
  },
  panelPeekTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  panelContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  companyLogoContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  companyLogoLarge: {
    width: 60,
    height: 60,
    backgroundColor: 'transparent',
  },
  detailsCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  jobMainDetails: {
    marginBottom: 24,
  },
  jobDetailsTitleText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  jobDetailsCompany: {
    fontSize: 16,
    marginBottom: 4,
  },
  jobDetailsLocationText: {
    fontSize: 14,
    marginBottom: 8,
  },
  jobDetailsPayText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  jobActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    marginBottom: 24,
  },
  jobActionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    padding: 10,
  },
  jobActionText: {
    marginTop: 8,
    fontSize: 12,
    fontWeight: '600',
  },
  toggleDetailsButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  toggleDetailsText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  swipeHintContainer: {
    position: 'absolute',
    left: 20,
    right: 20,
    marginHorizontal: 'auto',
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  // Add these new styles for the action labels
  passLabelContainer: {
    position: 'absolute',
    right: 20,
    top: '50%',
    backgroundColor: 'rgba(255, 59, 48, 0.9)', // Red with opacity
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    transform: [{ translateY: -25 }],
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  applyLabelContainer: {
    position: 'absolute',
    left: 20,
    top: '50%',
    backgroundColor: 'rgba(76, 217, 100, 0.9)', // Green with opacity
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    transform: [{ translateY: -25 }],
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  actionLabel: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 1,
  },
  tintOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    pointerEvents: 'none'
  },

  passLabelTopContainer: {
    position: 'absolute',
    right: '8%',
    top: '12%',
    zIndex: 150,
    backgroundColor: 'rgba(255, 59, 48, 0.98)',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 15,
    elevation: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.6,
    shadowRadius: 10,
    borderWidth: 2,
    borderColor: '#fff',
  },

  applyLabelTopContainer: {
    position: 'absolute',
    left: '8%',
    top: '12%',
    zIndex: 150,
    backgroundColor: 'rgba(76, 217, 100, 0.98)',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 15,
    elevation: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.6,
    shadowRadius: 10,
    borderWidth: 2,
    borderColor: '#fff',
  },

  actionLabelTop: {
    color: '#fff',
    fontWeight: '900',
    letterSpacing: 2,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 6,
  },

  // Styles for job cards without logos
  cardDetailsFullHeight: {
    width: '100%',
    height: '100%',
    borderRadius: 32,
    padding: 24,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 4,
  },
  titleSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  cardTitleLarge: {
    fontSize: 28,
    fontWeight: '800',
    textAlign: 'center',
    lineHeight: 34,
    letterSpacing: 0.5,
  },
  jobDetailsSection: {
    paddingTop: 16,
  },

});
